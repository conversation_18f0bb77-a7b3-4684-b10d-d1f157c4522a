from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
import subprocess
import time
import os
import platform
from logger_config import setup_logger

# 初始化日志
logger = setup_logger('ChromeDebugger')


class ChromeDebugger:
    def __init__(self, data_dir, driver_path, chrome_path=None, port=7777):
        """
        初始化Chrome调试器

        :param data_dir: 自定义数据目录路径
        :param driver_path: 浏览器驱动路径
        :param chrome_path: 谷歌浏览器可执行文件路径，默认为自动检测
        :param port: 调试端口，默认为9222
        """
        self.data_dir = os.path.abspath(data_dir)
        self.driver_path = os.path.abspath(driver_path)
        self.port = port
        self.browser_process = None
        self.driver = None

        # 确定Chrome路径
        self.chrome_path = chrome_path or self._find_chrome_path()

        # 确保数据目录存在
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)
            logger.info(f"创建数据目录: {self.data_dir}")

        # 验证文件是否存在
        self._validate_files()

    @staticmethod
    def _find_chrome_path():
        """自动检测Chrome浏览器路径"""
        system = platform.system()
        logger.info(f"检测到操作系统: {system}")

        if system == "Windows":
            paths = [
                os.path.join(os.environ.get("PROGRAMFILES", "C:\\Program Files"),
                             "Google\\Chrome\\Application\\chrome.exe"),
                os.path.join(os.environ.get("PROGRAMFILES(X86)", "C:\\Program Files (x86)"),
                             "Google\\Chrome\\Application\\chrome.exe")
            ]
        elif system == "Darwin":  # macOS
            paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
            ]
        elif system == "Linux":
            paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium",
                "/usr/bin/chromium-browser"
            ]
        else:
            raise Exception(f"不支持的操作系统: {system}")

        for path in paths:
            if os.path.exists(path):
                logger.info(f"找到Chrome浏览器: {path}")
                return path

        raise Exception("未找到Chrome浏览器，请手动指定路径")

    def _validate_files(self):
        """验证Chrome和驱动文件是否存在"""
        if not os.path.exists(self.chrome_path):
            raise FileNotFoundError(f"Chrome浏览器未找到: {self.chrome_path}")

        if not os.path.exists(self.driver_path):
            raise FileNotFoundError(f"Chrome驱动未找到: {self.driver_path}")

        logger.info("Chrome浏览器和驱动文件验证通过")

    def start_browser(self, url="about:blank"):
        """启动带有调试模式的谷歌浏览器"""
        if self.browser_process is not None and self.browser_process.poll() is None:
            raise Exception("浏览器已经在运行中")

        # 构建启动命令
        chrome_cmd = [
            self.chrome_path,
            f"--user-data-dir={self.data_dir}",
            f"--remote-debugging-port={self.port}",
            "--no-first-run",
            "--no-default-browser-check",
            url
        ]

        # 启动浏览器
        self.browser_process = subprocess.Popen(chrome_cmd)
        logger.info(f"已启动Chrome浏览器，进程ID: {self.browser_process.pid}")

        # 等待浏览器启动
        self._wait_for_browser()

        return self.browser_process.pid

    def _wait_for_browser(self, timeout=30, interval=1):
        """等待浏览器启动"""
        start_time = time.time()
        logger.info(f"等待Chrome浏览器在端口 {self.port} 上启动...")

        while time.time() - start_time < timeout:
            try:
                # 尝试连接到浏览器
                chrome_options = Options()
                chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.port}")

                # 使用Service类指定驱动路径
                service = Service(executable_path=self.driver_path)
                temp_driver = webdriver.Chrome(
                    service=service,
                    options=chrome_options
                )
                temp_driver.quit()
                logger.info("Chrome浏览器已成功启动并可以连接")
                return True
            except:
                time.sleep(interval)

        raise TimeoutError("等待浏览器启动超时")

    def connect_debugger(self):
        """连接到调试模式"""
        if self.driver is not None:
            return self.driver

        if self.browser_process is None or self.browser_process.poll() is not None:
            raise Exception("浏览器未运行，请先启动浏览器")

        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.port}")

        # 连接到已启动的浏览器
        service = Service(executable_path=self.driver_path)
        self.driver = webdriver.Chrome(
            service=service,
            options=chrome_options
        )

        logger.info("已成功连接到Chrome调试模式")
        return self.driver

    def close_browser(self):
        """关闭浏览器和驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("已关闭WebDriver连接")
            except Exception as e:
                logger.error(f"关闭WebDriver时出错: {str(e)}")
            self.driver = None

        if self.browser_process and self.browser_process.poll() is None:
            try:
                self.browser_process.terminate()
                self.browser_process.wait(timeout=10)
                logger.info("已关闭Chrome浏览器进程")
            except Exception as e:
                logger.error(f"关闭浏览器进程时出错: {str(e)}")
                try:
                    self.browser_process.kill()
                    logger.info("已强制关闭Chrome浏览器进程")
                except Exception as e:
                    logger.error(f"强制关闭浏览器进程时出错: {str(e)}")
            self.browser_process = None

    def __enter__(self):
        """上下文管理器进入方法"""
        self.start_browser()
        self.connect_debugger()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出方法"""
        self.close_browser()
