import os
import time
import datetime
import numpy as np
from typing import Optional, Union
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as ec
from selenium.webdriver.support.wait import WebDriverWait
from logger_config import setup_logger
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common import TimeoutException, ElementNotInteractableException

# 初始化日志
log = setup_logger('BasePage')


class BasePage:
    """基础页面类，封装了常用的页面操作方法"""

    def __init__(self, driver):
        """初始化方法，接收浏览器驱动实例"""
        self.driver = driver
        self.connect = None  # 预留连接属性，可根据需要使用

    def base_open_url(self, url: str, timeout: int = 10) -> bool:
        """打开特定URL并等待页面加载完成"""
        try:
            log.info(f"尝试打开URL: {url}")
            self.driver.get(url)

            # 等待页面标题出现，确认页面加载完成
            WebDriverWait(self.driver, timeout).until(
                lambda d: d.title != ""
            )

            log.info(f"成功打开URL: {url}，页面标题: {self.driver.title}")
            return True
        except Exception as e:
            log.error(f"打开URL失败: {url}，错误: {str(e)}")
            self.base_get_windows_img()
            return False

    def _human_mouse_path(self, start_x: int, start_y: int, end_x: int, end_y: int) -> list:
        """生成从起点到终点的自然鼠标轨迹（贝塞尔曲线）"""
        # 随机控制点（模拟手部抖动）
        c1 = (
            start_x + np.random.randint(-30, 30),
            start_y + np.random.randint(-30, 30)
        )
        c2 = (
            end_x + np.random.randint(-30, 30),
            end_y + np.random.randint(-30, 30)
        )

        # 生成15-25步轨迹（随机步数增加自然度）
        steps = np.random.randint(15, 25)
        path = []
        for t in np.linspace(0, 1, steps):
            # 贝塞尔曲线公式
            x = (1 - t) ** 3 * start_x + 3 * (1 - t) ** 2 * t * c1[0] + 3 * (1 - t) * t ** 2 * c2[0] + t ** 3 * end_x
            y = (1 - t) ** 3 * start_y + 3 * (1 - t) ** 2 * t * c1[1] + 3 * (1 - t) * t ** 2 * c2[1] + t ** 3 * end_y
            path.append((int(x), int(y)))
        return path

    def _get_element_center(self, element: WebElement) -> tuple:
        """获取元素中心点坐标（相对于页面）"""
        loc = element.location  # 元素左上角坐标
        size = element.size  # 元素宽高
        center_x = loc['x'] + size['width'] // 2
        center_y = loc['y'] + size['height'] // 2
        return (center_x, center_y)

    def base_mouse_action(self, action: str, loc: Optional[tuple] = None,
                          target_loc: Optional[tuple] = None, duration: int = 500) -> bool:
        """
        模拟鼠标操作，重点优化了点击操作的自然轨迹

        :param action: 鼠标动作类型
        :param loc: 操作的元素定位元组
        :param target_loc: 目标元素定位元组（仅拖拽用）
        :param duration: 拖拽持续时间(毫秒)
        :return: 操作成功返回True
        """
        try:
            action_chains = ActionChains(self.driver)
            element: Optional[WebElement] = None
            target_element: Optional[WebElement] = None

            if loc:
                element = self.base_find_element(loc)
                if not element:
                    return False

            if target_loc:
                target_element = self.base_find_element(target_loc)
                if not target_element:
                    return False

            # 获取当前鼠标位置（默认以视口中心为起点）
            viewport = self.driver.execute_script("return {w: window.innerWidth, h: window.innerHeight}")
            start_x, start_y = viewport['w'] // 2, viewport['h'] // 2
            path = []

            # 生成轨迹并执行移动（针对需要移动的动作）
            if action in ["click", "hover", "double_click", "right_click", "click_and_hold"]:
                # 目标坐标（元素中心点）
                end_x, end_y = self._get_element_center(element)
                # 生成轨迹
                path = self._human_mouse_path(start_x, start_y, end_x, end_y)

                # 执行轨迹移动
                action_chains.reset_actions()
                current_x, current_y = start_x, start_y
                for (x, y) in path:
                    # 相对移动（从当前位置到下一点）
                    action_chains.move_by_offset(x - current_x, y - current_y)
                    current_x, current_y = x, y
                    # 随机停顿（模拟人类反应延迟）
                    action_chains.pause(np.random.uniform(0.02, 0.05))

            # 拖拽动作处理
            if action == "drag_drop":
                # 从源元素到目标元素的轨迹
                start_x, start_y = self._get_element_center(element)
                end_x, end_y = self._get_element_center(target_element)
                path = self._human_mouse_path(start_x, start_y, end_x, end_y)

                action_chains.reset_actions()
                # 先移动到源元素并按下
                action_chains.move_to_element(element).pause(0.1).click_and_hold(element)
                # 执行拖拽轨迹
                current_x, current_y = start_x, start_y
                for (x, y) in path[1:]:  # 跳过起点（已在源元素上）
                    action_chains.move_by_offset(x - current_x, y - current_y)
                    current_x, current_y = x, y
                    action_chains.pause(np.random.uniform(0.03, 0.07))
                # 释放到目标元素
                action_chains.pause(0.1).release(target_element)

            # 执行最终动作
            action_map = {
                "click": lambda: action_chains.click().pause(0.1).perform(),
                "double_click": lambda: action_chains.double_click().pause(0.2).perform(),
                "right_click": lambda: action_chains.context_click().pause(0.1).perform(),
                "hover": lambda: action_chains.pause(0.5).perform(),
                "drag_drop": lambda: action_chains.perform(),
                "click_and_hold": lambda: action_chains.click_and_hold().perform(),
                "release": lambda: action_chains.release().perform()
            }

            if action not in action_map:
                log.error(f"不支持的鼠标动作: {action}")
                return False

            action_map[action]()
            log.info(f"执行鼠标动作: {action}，元素: {loc}，轨迹步数: {len(path)}")
            return True

        except TimeoutException:
            log.error(f"鼠标操作超时，元素定位: {loc}")
            self.base_get_windows_img()
            return False
        except Exception as e:
            log.error(f"鼠标操作失败: {str(e)}, 元素定位: {loc}")
            self.base_get_windows_img()
            return False

    def base_keyboard_action(self, action: str, loc: Optional[tuple] = None,
                             keys: Optional[Union[str, tuple]] = None, timeout: int = 30,
                             wait_type: str = "visible") -> bool:
        """
        模拟键盘操作

        :param action: 键盘动作类型
        :param loc: 操作的元素定位元组（可选）
        :param keys: 按键或文本内容
        :param timeout: 元素等待超时时间
        :param wait_type: 元素等待类型
        :return: 操作成功返回True
        """
        try:
            # 获取目标元素（可选）
            target_element = None
            if loc:
                target_element = self.base_find_element(loc, timeout=timeout, wait_type=wait_type)
                if not target_element:
                    return False

            # 初始化动作链
            action_chains = ActionChains(self.driver)

            # 绑定操作对象（元素或全局）
            if target_element:
                action_chain = action_chains.move_to_element(target_element)
            else:
                action_chain = action_chains

            # 执行键盘动作
            if action == "send_keys":
                if not isinstance(keys, str):
                    log.error("send_keys动作的keys必须是字符串")
                    return False
                action_chain.send_keys(keys).perform()
                log.info(f"向元素{loc or '当前焦点'}输入文本: {keys}")

            elif action == "single_key":
                if not hasattr(keys, '__name__'):
                    log.error(f"不支持的单键: {keys}")
                    return False
                action_chain.send_keys(keys).perform()
                log.info(f"向元素{loc or '当前焦点'}发送单键: {keys.__name__}")

            elif action == "combo_keys":
                if not isinstance(keys, tuple) or len(keys) < 2:
                    log.error("combo_keys动作的keys必须是包含至少两个元素的元组")
                    return False
                # 按下组合键
                for key in keys:
                    action_chain.key_down(key)
                action_chain.perform()
                # 释放修饰键
                for key in keys:
                    if key in (Keys.CONTROL, Keys.SHIFT, Keys.ALT):
                        action_chain.key_up(key)
                action_chain.perform()
                log.info(
                    f"向元素{loc or '当前焦点'}发送组合键: {[k.__name__ if hasattr(k, '__name__') else k for k in keys]}")

            elif action == "clear":
                if not loc:
                    log.error("clear动作必须指定元素定位loc")
                    return False
                # 先全选再删除
                action_chain.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).send_keys(Keys.DELETE).perform()
                log.info(f"清空元素{loc}内容")

            else:
                log.error(f"不支持的键盘动作: {action}")
                return False

            return True

        except ElementNotInteractableException:
            log.error(f"元素{loc}不可交互，无法执行键盘操作")
            self.base_get_windows_img()
            return False
        except TimeoutException:
            log.error(f"键盘操作超时，元素定位: {loc}")
            self.base_get_windows_img()
            return False
        except Exception as e:
            log.error(f"键盘操作失败: {str(e)}, 元素定位: {loc}")
            self.base_get_windows_img()
            return False

    def base_switch_window(self, target: str = "new", index: Optional[int] = None,
                           title: Optional[str] = None, url: Optional[str] = None,
                           timeout: int = 10) -> bool:
        """切换浏览器窗口或标签页"""
        try:
            WebDriverWait(self.driver, timeout).until(
                lambda d: len(d.window_handles) > 0
            )

            current_handle = self.driver.current_window_handle
            all_handles = self.driver.window_handles
            log.info(f"当前窗口数量: {len(all_handles)}, 当前窗口句柄: {current_handle[-10:]}")

            # 窗口切换逻辑
            if target == "new" and len(all_handles) > 1:
                new_handle = [h for h in all_handles if h != current_handle][-1]
                self.driver.switch_to.window(new_handle)
            elif target == "main":
                self.driver.switch_to.window(all_handles[0])
            elif target == "current":
                return True
            elif index is not None and 0 <= index < len(all_handles):
                self.driver.switch_to.window(all_handles[index])
            elif title:
                for handle in all_handles:
                    self.driver.switch_to.window(handle)
                    if title in self.driver.title:
                        return True
                log.error(f"未找到标题包含 '{title}' 的窗口")
                return False
            elif url:
                for handle in all_handles:
                    self.driver.switch_to.window(handle)
                    if url in self.driver.current_url:
                        return True
                log.error(f"未找到URL包含 '{url}' 的窗口")
                return False
            else:
                log.error("未指定有效的切换目标")
                return False

            log.info(f"切换后页面标题: {self.driver.title}, URL: {self.driver.current_url}")
            return True

        except TimeoutException:
            log.error(f"切换窗口超时，超过 {timeout} 秒")
            self.base_get_windows_img()
            return False
        except Exception as e:
            log.error(f"切换窗口失败: {str(e)}")
            self.base_get_windows_img()
            return False

    def base_find_element(self, loc: tuple, timeout: int = 77, poll: float = 0.5,
                          wait_type: str = "clickable") -> Optional[WebElement]:
        """
        显示等待查找元素

        :param loc: 元素定位元组
        :param timeout: 超时时间
        :param poll: 轮询频率
        :param wait_type: 等待类型
        :return: 找到的元素(WebElement)或None
        """
        try:
            wait = WebDriverWait(self.driver, timeout=timeout, poll_frequency=poll)

            wait_strategies = {
                "clickable": ec.element_to_be_clickable(loc),
                "visible": ec.visibility_of_element_located(loc),
                "visible_off": ec.invisibility_of_element_located(loc),
                "presence_of": ec.presence_of_element_located(loc),
                "findonly": lambda x: x.find_element(*loc)
            }

            if wait_type not in wait_strategies:
                log.error(f"不支持的等待类型: {wait_type}")
                return None

            return wait.until(wait_strategies[wait_type])

        except TimeoutException:
            log.error(f'【显性等待】{timeout}秒超时, 等待方式: {wait_type}, 元素定位: {loc}')
            self.base_get_windows_img()
            return None
        except Exception as e:
            log.error(f"查找元素发生错误: {str(e)}, 元素定位: {loc}")
            self.base_get_windows_img()
            return None

    def base_click(self, loc: tuple, timeout: int = 77, wait_type: str = "clickable") -> bool:
        """点击元素"""
        el = self.base_find_element(loc, timeout=timeout, wait_type=wait_type)
        if el:
            try:
                el.click()
                log.info(f"元素被点击: {el.text if el.text else str(loc)}")
                return True
            except Exception as e:
                log.error(f"无法单击元素, 定位: {loc}, 错误: {str(e)}")
                self.base_get_windows_img()
                return False
        return False

    def base_send_keys(self, loc: tuple, value: str, timeout: int = 30,
                       wait_type: str = "clickable", clean: str = "clean",
                       verify: bool = True) -> bool:
        """向元素发送文本，优化特殊输入框处理"""
        el = self.base_find_element(loc, timeout=timeout, wait_type=wait_type)
        if not el:
            return False

        try:
            # 确保元素获得焦点
            self.driver.execute_script("arguments[0].focus();", el)
            time.sleep(0.3)

            # 清除原有内容
            if clean == "clean":
                current_value = el.get_attribute('value') or ""
                if current_value:
                    el.clear()
                    el.send_keys(Keys.CONTROL + "a")
                    el.send_keys(Keys.DELETE)
                    WebDriverWait(self.driver, 5).until(
                        lambda d: el.get_attribute('value') == ""
                    )
                    log.info(f'已清除元素原有内容: {current_value}')

            # 输入处理
            input_success = False
            # 优先使用JavaScript输入
            try:
                self.driver.execute_script(f"arguments[0].value = '{value}';", el)
                # 触发输入事件
                self.driver.execute_script("""
                    var event = new Event('input', { bubbles: true });
                    arguments[0].dispatchEvent(event);
                    var changeEvent = new Event('change', { bubbles: true });
                    arguments[0].dispatchEvent(changeEvent);
                """, el)
                input_success = True
            except Exception as e:
                log.warning(f"JavaScript输入失败: {str(e)}, 尝试常规输入")
                el.send_keys(value)
                input_success = True

            if not input_success:
                log.error("所有输入方式均失败")
                self.base_get_windows_img()
                return False

            # 验证输入结果
            if verify:
                def value_verified(driver) -> bool:
                    actual = el.get_attribute('value') or el.text
                    return value in actual

                WebDriverWait(self.driver, 10).until(value_verified)
                log.info(f"输入验证成功，元素值为: {value}")
                return True

            return True

        except TimeoutException:
            actual_value = el.get_attribute('value')
            log.error(f"输入验证超时，预期值: {value}, 实际值: {actual_value}")
            self.base_get_windows_img()
            return False
        except ElementNotInteractableException:
            log.error(f"元素不可交互，可能被遮挡或禁用: {loc}")
            self.base_get_windows_img()
            return False
        except Exception as e:
            log.error(f"输入内容失败: {str(e)}, 定位: {loc}")
            self.base_get_windows_img()
            return False

    def base_get_text(self, loc: tuple, timeout: int = 30, wait_type: str = "clickable") -> str:
        """获取元素文本"""
        el = self.base_find_element(loc, timeout=timeout, wait_type=wait_type)
        if el:
            try:
                text = el.text.strip()
                log.info(f"获取元素文本: {text}")
                return text
            except Exception as e:
                log.error(f"获取元素文本失败: {str(e)}, 定位: {loc}")
                self.base_get_windows_img()
        return ""

    def base_get_windows_img(self) -> None:
        """截取当前页面截图"""
        try:
            screenshots_dir = os.path.join(os.getcwd(), 'screenshots')
            os.makedirs(screenshots_dir, exist_ok=True)

            rq_time = time.strftime('%Y%m%d%H%M%S', time.localtime())
            screen_name = os.path.join(screenshots_dir, f'{rq_time}.png')
            self.driver.get_screenshot_as_file(screen_name)
            log.info(f"屏幕截图已保存: {screen_name}")

        except Exception as e:
            log.error(f"截屏失败: {str(e)}")

    def base_refresh(self) -> None:
        """刷新当前页面"""
        try:
            self.driver.refresh()
            log.info("页面已刷新")
        except Exception as e:
            log.error(f"页面刷新失败: {str(e)}")

    def base_is_element_present(self, loc: tuple) -> bool:
        """检查元素是否存在"""
        try:
            self.driver.find_element(*loc)
            log.info(f"元素存在: {loc}")
            return True
        except Exception:
            log.info(f"元素不存在: {loc}")
            return False

    @staticmethod
    def base_get_current_date(format_type: str = "YYYYMMDD") -> str:
        """
        获取当前日期

        :param format_type: 日期格式类型
        :return: 格式化的日期字符串
        """
        try:
            now = datetime.datetime.now()

            format_map = {
                "YYYYMMDD": now.strftime('%Y%m%d'),
                "YYYY-MM-DD": now.strftime('%Y-%m-%d'),
                "YYYY/MM/DD": now.strftime('%Y/%m/%d'),
                "DD/MM/YYYY": now.strftime('%d/%m/%Y'),
                "timestamp": str(int(now.timestamp()))
            }

            result = format_map.get(format_type, now.strftime('%Y%m%d'))
            log.info(f"获取当前日期: {result} (格式: {format_type})")
            return result

        except Exception as e:
            log.error(f"获取当前日期失败: {str(e)}")
            return ""

    @staticmethod
    def base_waybill_to_truck_no(waybill: Optional[str]) -> Optional[str]:
        """将运单号转换为派车单号（替换Y为P）"""
        if waybill and isinstance(waybill, str):
            result = waybill.replace('Y', 'P')
            log.info(f"运单号转换: {waybill} -> {result}")
            return result
        log.warning(f"无效的运单号: {waybill}")
        return waybill