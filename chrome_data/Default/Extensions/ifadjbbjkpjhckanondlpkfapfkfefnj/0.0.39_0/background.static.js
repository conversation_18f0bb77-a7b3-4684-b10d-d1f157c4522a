const e={NoSuchWindow:"NoSuchWindow",NoSuchTab:"NoSuchTab",NoSuchFrame:"NoSuchFrame",NoSuchElement:"NoSuchElement",ScriptsInjectionFailure:"ScriptsInjectionFailure",InvalidPage:"InvalidPage",HandlerNotFound:"HandlerNotFound",JsScriptError:"JsScriptError",BackgroundScriptError:"BackgroundScriptError",UnsupportedNetworkMonitorPage:"UnsupportedNetworkMonitorPage"};class t extends Error{constructor(e,n){super(e),this.message=e,this.errorCode=n,this.name=t.name}}const n={Overlay_setShowViewportSizeOnResize:"Overlay.setShowViewportSizeOnResize",Page_getLayoutMetrics:"Page.getLayoutMetrics",Page_captureScreenshot:"Page.captureScreenshot",Emulation_resetPageScaleFactor:"Emulation.resetPageScaleFactor",Emulation_setDeviceMetricsOverride:"Emulation.setDeviceMetricsOverride",Emulation_clearDeviceMetricsOverride:"Emulation.clearDeviceMetricsOverride",Runtime_evaluate:"Runtime.evaluate",Network_getResponseBody:"Network.getResponseBody",Network_enable:"Network.enable",Network_disable:"Network.disable",Input_setIgnoreInputEvents:"Input.setIgnoreInputEvents"};class i{constructor(e){this.debuggerTaget=e}static async ensureDebuggerAttached(e){try{await chrome.debugger.attach({tabId:e},"1.2")}catch(e){}}async sendCommand(e,t={}){return await i.ensureDebuggerAttached(this.debuggerTaget.tabId),await chrome.debugger.sendCommand(this.debuggerTaget,e,t)}}class r{constructor(e){this.url=e.url,this.type=e.type,this.status=e.status,this.headers=e.headers,this.body=e.body,this.base64Encoded=e.base64Encoded,this.requestId=e.requestId}}class a{constructor(){this.queue=[],this.limit=500}add(e){this.queue.push(e),this.queue.length>this.limit&&this.queue.shift()}clear(){this.queue=[]}}class s{constructor(e,t,n){this.url=e||"",this.useRegex=t||!1,this.resourceType=n||"",this.useRegex&&(this.regexpUrl=new RegExp(this.url))}isMatch(e,t){return-1!==this.resourceType.indexOf(t)&&(!this.url.length||!(this.useRegex&&!this.regexpUrl.test(e))&&!(!this.useRegex&&-1===e.indexOf(this.url)))}}class l{constructor(e,t){this.tabId=e,this.condition=t,this.result=new a}async capture(e,t){if("WebSocket"==e){if(-1===this.resourceType.indexOf("WebSocket"))return;t.response&&1==t.response.opcode&&this.result.add(new r({type:"WebSocket",body:t.response.payloadData}))}else this.condition.isMatch(t.response.url,e)&&this.result.add(new r({url:t.response.url,requestId:t.requestId,type:e,status:t.response.status,headers:t.response.headers}))}async getResponseBody(e,t){let r;try{var a=new i({tabId:e});r=await a.sendCommand(n.Network_getResponseBody,{requestId:t})}catch(e){}return r||{body:"",base64Encoded:!1}}dispose(){this.result.clear()}}class o{constructor(){this.networkCapturerMap=new Map}async attach(e){let t=(await this.getTab(e)).id,r=this.networkCapturerMap.get(t);null!=r&&(r.dispose(),this.networkCapturerMap.delete(t));let a=new s(e.url,e.useRegex,e.resourceType);this.networkCapturerMap.set(t,new l(t,a));const o=new i({tabId:t});await o.sendCommand(n.Network_enable)}async detach(e){let t=(await this.getTab(e)).id,r=this.networkCapturerMap.get(t);if(null!=r){r.dispose(),this.networkCapturerMap.delete(t);const e=new i({tabId:t});await e.sendCommand(n.Network_disable)}}async getResponses(e){let t=(await this.getTab(e)).id,n=this.networkCapturerMap.get(t);if(null!=n){for(let e of n.result.queue)if(null==e.body||""===e.body){let i=await n.getResponseBody(t,e.requestId);e.body=i.body,e.base64Encoded=i.base64Encoded}return n.result.queue}return[]}async setResponse(e,t,n){var i=e.result.queue.find((e=>e.requestId==n));if(i){let n=await e.getResponseBody(t,i.requestId);""!=n.body&&(i.body=n.body,i.base64Encoded=n.base64Encoded)}}registerChromeEvents(){chrome.tabs.onDetached.addListener((e=>this.detach(e.id))),chrome.debugger.onEvent.addListener(((e,t,n)=>{let i=this.networkCapturerMap.get(e.tabId);null!=i&&("Network.responseReceived"==t&&i.capture(n.type,n),"Network.webSocketFrameReceived"==t&&i.capture("WebSocket",n),"Network.webSocketFrameSent"==t&&i.capture("WebSocket",n),"Network.loadingFinished"==t&&this.setResponse(i,e.tabId,n.requestId))}))}async getTab(n){let i=null;try{i=await chrome.tabs.get(n.tabId)}catch{throw new t("Page not found",e.NoSuchTab)}if(null!=i&&!v(i.url))return i;throw new t("Unsupported network monitor page",e.UnsupportedNetworkMonitorPage)}}class c{constructor(e,t,n,i){this.name=e,this.code=t,this.message=n,this.stack=i}}class u{constructor(e){this.requestId=e.requestId,this.name=e.name,this.parameters=e.parameters}}class m{constructor(e,t,n){this.result=t,this.error=n,this.requestId=e.requestId,this.name=e.name}}class h{constructor(){}static invalidProtocolRegex=/^([^\w]*)(javascript|data|vbscript)/im;static htmlEntitiesRegex=/&#(\w+)(^\w|;)?/g;static htmlCtrlEntityRegex=/&(newline|tab);/gi;static ctrlCharactersRegex=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim;static urlSchemeRegex=/^.+(:|&colon;)/gim;static relativeFirstCharacters=[".","/"];static sanitizeUrl=function(e){var t,n=(t=e||"",t.replace(h.htmlEntitiesRegex,(function(e,t){return String.fromCharCode(t)}))).replace(h.htmlCtrlEntityRegex,"").replace(h.ctrlCharactersRegex,"").trim();if(!n)return"about:blank";if(function(e){return h.relativeFirstCharacters.indexOf(e[0])>-1}(n))return n;var i=n.match(h.urlSchemeRegex);if(!i)return n;var r=i[0];return h.invalidProtocolRegex.test(r)?"about:blank":n}}class d{constructor(e,t,n,i){this.id=e,this.tabId=t,this.parentFrameId=n,this.url=i}static get MainFrame(){return 0}}class f{static get GetHoveringElementRequest(){return"GetHoveringElementRequest"}static get SelectElementRequest(){return"SelectElementRequest"}static get SelectSimilarElementRequest(){return"SelectSimilarElementRequest"}static IsPointRequest(e){return e==f.GetHoveringElementRequest||e==f.SelectElementRequest||e==f.SelectSimilarElementRequest}}class g{constructor(){this.applicationName="com.bazhuayu.rpa.messagehost",this.handler={},this.retryTimeout=3e3}on(e,t){this.handler[e]=t}removeHandler(e){delete this.handler[e]}addDefaultHandler(e){this.defaultHandler=e}connect(){try{this.initializeConnectedPort(chrome.runtime.connectNative(this.applicationName))}catch(e){}}onDisconnect(e){setTimeout((()=>{this.connect()}),this.retryTimeout)}post(e){this.msgPort.postMessage(e)}initializeConnectedPort(e){this.msgPort=e,this.msgPort.onMessage.addListener((e=>{var t=new u(e);this.onMessageReceived(t)})),this.msgPort.onDisconnect.addListener((e=>{this.onDisconnect(e)}))}async onMessageReceived(n){let i=this.handler[n.name];i||(i=this.defaultHandler);try{let e=i(n);if(null==e)var r=new m(n,null);else if(void 0!==e.then&&"function"==typeof e.then)if(e=await e,null!=e&&void 0!==e.requestId)r=e;else r=new m(n,e);else r=new m(n,e);r.requestId==n.requestId&&this.post(r)}catch(i){i.message.startsWith("No tab with id:")&&(i=new t("Page not found",e.NoSuchTab));r=new m(n,0,new c(i.name,null!=i.errorCode&&null!==i.errorCode?i.errorCode:e.BackgroundScriptError,i.message,i.stack));this.post(r)}}}class p{static async getAllFrames(e){return(await chrome.webNavigation.getAllFrames({tabId:e.id})).map((t=>new d(t.frameId,e.id,t.parentFrameId,t.url)))}static async findFrameByIframeXPath(n,i,r){const a=function(e){const t=e.split("/"),n=[];let i=[];for(const e of t){i.push(e);const t=e.toLowerCase().split("[")[0];"frame"!==t&&"iframe"!==t||(0==n.length?n.push(i.join("/")):n.push("/"+i.join("/")),i=[])}return n}(i),s=await p.getAllFrames(n),l=await p._findFrameByIframeXPath([...a],n,d.MainFrame,s);if(0===l.length||a.length!==l.length){if(r)throw new t("No such frame",e.NoSuchFrame);return[]}return l}static async findFrameByPoint(e,t){const n=await p.getAllFrames(e);return await p._findFrameByPoint(t,e,d.MainFrame,n)}static async _findFrameByPoint(e,t,n,i){const r={tabId:t.id,frameIds:[n]},a={parameters:{point:e,parentFrameId:n,allFrames:i}},s=await w(r,"FindFrameElementFromPointRequest",a),{frameInfo:l,hasChildren:o}=s?.result??{};let c=[];if(l&&(c.push(l),l.frame&&o)){const n=l.bounding,r={x:e.x-n.x,y:e.y-n.y},a=await p._findFrameByPoint(r,t,l.frame.id,i);c=c.concat(a)}return c}static async _findFrameByIframeXPath(e,t,n,i){if(!e.length)return[];const r=e.shift(),a={tabId:t.id,frameIds:[n]},s={parameters:{iframeXpath:r,parentFrameId:n,allFrames:i}},l=[],o=await w(a,"FindFramesByXPathRequest",s);if(null==o||null==o||null==o.result)return l;const c=o.result;if(!c||0===c.length)return[];if(!e.length)return[c[0]];for(const n of c){const r=await p._findFrameByIframeXPath([...e],t,n.frame.id,i);if(r.length===e.length){l.push(n,...r);break}}return l}}class E{constructor(e){this.communicatorToNative=e,this.lastSimiliarSelectionTarget=[],this.networkMonitor=new o,this.addDefaultHandler(),this.registerHostEvents(),this.registerChromeEvents(e),this.networkMonitor.registerChromeEvents()}registerChromeEvents(e){chrome.tabs.onRemoved.addListener(((t,n)=>{e.post({eventName:"removed",tabId:t})})),chrome.webNavigation.onCompleted.addListener((async function(e){try{var t=0==e.frameId?{tabId:e.tabId,allFrames:!1}:{tabId:e.tabId,allFrames:!1,frameIds:[e.frameId]},n=-1!=e.parentFrameId&&0!=e.parentFrameId?{tabId:e.tabId,allFrames:!1,frameIds:[e.parentFrameId]}:{tabId:e.tabId,allFrames:!1};await w(n,"InitializeFrameIndexRequest",{parameters:{}}),w(t,"SetFrameIdRequest",{parameters:{frameId:e.frameId}})}catch{}})),chrome.tabs.onUpdated.addListener(((t,n,i)=>{n.hasOwnProperty("status")&&"loading"===n.status&&n.hasOwnProperty("url")?e.post({eventName:"loading",tabId:t,url:i.url}):n.hasOwnProperty("status")&&"complete"==n.status?e.post({eventName:"completed",tabId:t}):n.hasOwnProperty("title")&&e.post({eventName:"titleChanged",title:n.title,tabId:t})})),chrome.windows.onCreated.addListener((t=>{e.post({eventName:"windowCreated",windowId:t.id})})),chrome.windows.onRemoved.addListener((t=>{null!=this.browserWindowRegistry&&this.browserWindowRegistry.delete(t),e.post({eventName:"windowRemoved",windowId:t})})),chrome.windows.onFocusChanged.addListener((t=>{t!=chrome.windows.WINDOW_ID_NONE&&e.post({eventName:"windowFocus",windowId:t})}))}static onGetVersion(){return chrome.runtime.getManifest().version}static onCreateNewTab(e){const t=P?h.sanitizeUrl(e.url):e.url;return chrome.tabs.create({url:t})}static onGetAllTabRequest(e){return chrome.tabs.query({})}static onGetTabRequest(e){return chrome.tabs.query(e)}static async onGetTabByIdRequest(n){try{var i=await chrome.tabs.get(n.tabId)}catch(n){throw new t("Page not found",e.NoSuchTab)}return i}static onGetAllWindowRequest(e){return chrome.windows.getAll({populate:!0,windowTypes:["normal"]})}static async onCloseAllWindowRequest(e){(await chrome.windows.getAll({populate:!0})).forEach((e=>{chrome.windows.remove(e.id)}))}static onGetBrowserWindowRequest(e){return chrome.windows.get(e.windowId)}static onNavigate(e){const t=P?h.sanitizeUrl(e.url):e.url;return N((()=>chrome.tabs.update(e.tabId,{url:t})))}static onRefreshPage(e){return chrome.tabs.reload(e.tabId,{bypassCache:!0})}static onGoBackward(e){return chrome.tabs.goBack(e.tabId)}static onGoForward(e){return chrome.tabs.goForward(e.tabId)}static onActivateTab(e){return N((()=>chrome.tabs.update(e.tabId,{active:!0})))}static onCloseTab(e){return N((()=>chrome.tabs.remove(e.tabId)))}static onGetTabZoom(e){return chrome.tabs.getZoom(e.tabId)}static async getTabByMessage(n){let i=null;try{if(n&&n.hasOwnProperty("tabId"))i=await chrome.tabs.get(n.tabId);else if(n&&n.hasOwnProperty("windowId")){i=(await chrome.tabs.query({active:!0,windowId:n.windowId}))[0]}else{i=(await chrome.tabs.query({active:!0,lastFocusedWindow:!0}))[0]}}catch(n){throw new t("Page not found",e.NoSuchTab)}if(null==i)throw new t("Page not found",e.NoSuchTab);return i}static async onGetCurrentTabZoom(e){var t=await chrome.tabs.query({active:!0,windowId:e.windowId});if(t.length>0){var n=t[0];return chrome.tabs.getZoom(n.id)}return 1}static async onClearCookies(e){if(null!=e.name)return await chrome.cookies.remove({url:e.url,name:e.name});for(var t=await chrome.cookies.getAll({url:e.url}),n=0;n<t.length;n++)await chrome.cookies.remove({url:e.url,name:t[n].name})}static async onSetCookieRequest(e){for(var t=e.domain.split(".");t.length>=2;){e.url="https://"+t.join(".");try{return void await chrome.cookies.set(e)}catch(e){t.shift()}}}static async onGetCookieRequest(e){return await chrome.cookies.getAll({url:e.url})}static onRunJavaScript(e){return new Promise((t=>{const r={tabId:e.tabId},a=new i(r);i.ensureDebuggerAttached(e.tabId).then((()=>{a.sendCommand(n.Runtime_evaluate,{expression:e.code}).then((e=>{null!=e.result.value?t(e.result.value+""):t()}))}))}))}static onDownloadByUrlRequest(e){return chrome.downloads.download(e)}static onSearchDownloadItemsRequest(e){return e.id?chrome.downloads.search({id:e.id}):e.orderBy?chrome.downloads.search({limit:e.limit,orderBy:e.orderBy}):[]}registerHostEvents(){this.communicatorToNative.on("GetVersionRequest",(e=>E.onGetVersion(e.parameters))),this.communicatorToNative.on("GetTabRequest",(e=>E.onGetTabRequest(e.parameters))),this.communicatorToNative.on("GetAllTabRequest",(e=>E.onGetAllTabRequest(e.parameters))),this.communicatorToNative.on("GetTabByIdRequest",(e=>E.onGetTabByIdRequest(e.parameters))),this.communicatorToNative.on("GetAllWindowRequest",(e=>E.onGetAllWindowRequest(e.parameters))),this.communicatorToNative.on("GetBrowserWindowRequest",(e=>E.onGetBrowserWindowRequest(e.parameters))),this.communicatorToNative.on("CreateNewTabRequest",(e=>E.onCreateNewTab(e.parameters))),this.communicatorToNative.on("NavigateRequest",(e=>E.onNavigate(e.parameters))),this.communicatorToNative.on("CloseTabRequest",(e=>E.onCloseTab(e.parameters))),this.communicatorToNative.on("ClearCookiesRequest",(e=>E.onClearCookies(e.parameters))),this.communicatorToNative.on("SetCookieRequest",(e=>E.onSetCookieRequest(e.parameters))),this.communicatorToNative.on("GetCookiesRequest",(e=>E.onGetCookieRequest(e.parameters))),this.communicatorToNative.on("ActivateTabRequest",(e=>E.onActivateTab(e.parameters))),this.communicatorToNative.on("RunJavaScriptRequest",(e=>E.onRunJavaScript(e.parameters))),this.communicatorToNative.on("RefreshPageRequest",(e=>E.onRefreshPage(e.parameters))),this.communicatorToNative.on("GoForwardRequest",(e=>E.onGoForward(e.parameters))),this.communicatorToNative.on("GoBackwardRequest",(e=>E.onGoBackward(e.parameters))),this.communicatorToNative.on("GetCurrentTabZoomRequest",(e=>E.onGetCurrentTabZoom(e.parameters))),this.communicatorToNative.on("GetTabZoomRequest",(e=>E.onGetTabZoom(e.parameters))),this.communicatorToNative.on("DownloadByUrlRequest",(e=>E.onDownloadByUrlRequest(e.parameters))),this.communicatorToNative.on("SearchDownloadItemsRequest",(e=>E.onSearchDownloadItemsRequest(e.parameters))),this.communicatorToNative.on("CloseAllWindowRequest",(e=>E.onCloseAllWindowRequest(e.parameters))),this.communicatorToNative.on("StartMonitorNetworkRequest",(e=>this.networkMonitor.attach(e.parameters))),this.communicatorToNative.on("StopMonitorNetworkRequest",(e=>this.networkMonitor.detach(e.parameters))),this.communicatorToNative.on("GetMonitorResponseRequest",(e=>this.networkMonitor.getResponses(e.parameters))),this.communicatorToNative.on("GetElementRectRequest",(e=>this.onGetElementRect(e))),this.communicatorToNative.on("GetHoveringElementRequest",(e=>this.onGetHoveringElement(e))),this.communicatorToNative.on("SelectElementRequest",(e=>this.onSelectElement(e))),this.communicatorToNative.on("SelectSimilarElementRequest",(e=>this.onSelectSimilarElement(e))),this.communicatorToNative.on("ClearSimiliarElementSelectionStateRequest",(e=>this.onClearSimiliarElementSelectionStateRequest(e))),this.communicatorToNative.on("GetElementObjectsRequest",(e=>this.onGetElementObjects(e))),this.communicatorToNative.on("CaptureElementImageRequest",(e=>this.onCaptureElementImageRequest(e))),this.communicatorToNative.on("CapturePageImageRequest",(e=>this.onCapturePageImageRequest(e))),this.communicatorToNative.on("GetFrameIdByFrameXPathRequest",(e=>this.onGetFrameIdByFrameXPathRequest(e))),this.communicatorToNative.on("IsElementOnViewportRequest",(e=>this.onIsElementOnViewport(e))),this.communicatorToNative.on("IsVisibleElementRequest",(e=>this.onIsVisibleElement(e))),this.communicatorToNative.on("QueryElementCountByXpathRequest",(e=>this.onQueryElementCountByXpath(e))),this.communicatorToNative.on("FindElementsByXpathRequest",(e=>this.onFindElementsByXpath(e))),this.communicatorToNative.on("FindSingleElementByXpathRequest",(e=>this.onFindSingleElementByXpath(e))),this.communicatorToNative.on("SelectSimilarFirstElementRequest",(e=>this.onSelectSimilarFirstElement(e))),this.communicatorToNative.on("StopLoadingRequest",(e=>this.onStopLoading(e)))}addDefaultHandler(){this.communicatorToNative.addDefaultHandler((e=>this.onDefaultHandlerToContent(e)))}async onDefaultHandlerToContent(e){const{tab:t,target:n,newMessage:i}=await x(e);return await w(n,i.name,i)}async onClearSimiliarElementSelectionStateRequest(e){if(this.lastSimiliarSelectionTarget.length>0){for(var t=0;t<this.lastSimiliarSelectionTarget.length;t++)await w(this.lastSimiliarSelectionTarget[t],"ClearSimilarSelectRequest",e);this.lastSimiliarSelectionTarget=[]}}async onGetFrameIdByFrameXPathRequest(e){const{tab:t,target:n,newMessage:i}=await x(e);return n.frameIds.length>0?n.frameIds[0]:d.MainFrame}async onCapturePageImageRequest(e){const t=e.parameters,r={tabId:t.tabId},a={name:e.name,parameters:{behavior:"smooth",scrollTo:"Bottom"},requestId:e.requestId};t.whole&&(await w(r,"ScrollRequest",a),await b(1.5));const s=new i(r),l=[];if(t.whole){await s.sendCommand(n.Overlay_setShowViewportSizeOnResize,{show:!1}),await b(.5);var o=await s.sendCommand(n.Page_getLayoutMetrics);await s.sendCommand(n.Emulation_resetPageScaleFactor),await b(.5),await s.sendCommand(n.Emulation_setDeviceMetricsOverride,{deviceScaleFactor:1,mobile:!1,width:o.contentSize.width,height:o.contentSize.height});for(var c=0;c<o.contentSize.height;){var u=o.contentSize.height-c>3e3?3e3:o.contentSize.height-c,m=o.contentSize.width,h={x:0,y:c,width:m,height:u,scale:1},d=await s.sendCommand(n.Page_captureScreenshot,{clip:h,format:t.format,fromSurface:!0,quality:100});c+=3e3,l.push({img:d.data,width:m,height:u}),await b(.5)}await s.sendCommand(n.Emulation_clearDeviceMetricsOverride)}else{d=await s.sendCommand(n.Page_captureScreenshot,{format:t.format});l.push({img:d.data})}return l}async onCaptureElementImageRequest(e){const{tab:t,target:r,newMessage:a}=await x(e),s={tabId:t.id},l=new i(s);await l.sendCommand(n.Overlay_setShowViewportSizeOnResize,{show:!1}),await b(.5);let o=await l.sendCommand(n.Page_getLayoutMetrics);const c=o.visualViewport.zoom;await b(.5);let u=await this.onIsElementOnViewport(e);if(!u){const[e]=await E.getDevicePixelRatio(t.id),i=e/c;await l.sendCommand(n.Emulation_setDeviceMetricsOverride,{deviceScaleFactor:i,mobile:!1,width:o.contentSize.width,height:o.contentSize.height})}u=await this.onIsElementOnViewport(e),u||await w(r,"ScrollToElementRequest",a),await b(1);const m=await w(r,"GetElementRectRequest",a);if(null==m)return null;if(void 0!==m.error)return m;const h=m.result;if(0==h.length)return null;let d=h[0],f=a.frameInfos;Array.isArray(f)&&f.length>0&&(f=await p.findFrameByIframeXPath(t,e.parameters.selector.iframeXpath,!0),f.forEach((e=>{d.x+=e.bounding.x,d.y+=e.bounding.y})));const g={x:d.x*c,y:d.y*c,width:d.width*c,height:d.height*c,scale:1};o=await l.sendCommand(n.Page_getLayoutMetrics),g.x+=o.visualViewport.pageX*c,g.y+=o.visualViewport.pageY*c,await l.sendCommand(n.Input_setIgnoreInputEvents,{ignore:!0});try{const t=await l.sendCommand(n.Page_captureScreenshot,{clip:g,format:e.parameters.format,fromSurface:!0,quality:100});return await l.sendCommand(n.Emulation_clearDeviceMetricsOverride),t.data}finally{await b(.5),await l.sendCommand(n.Input_setIgnoreInputEvents,{ignore:!1})}}async onIsElementOnViewport(e){let{tab:t,target:n,newMessage:i}=await x(e),r=await w(n,"GetElementRectRequest",i);if(null===r||null!=r.error)return!1;let a=r.result[0];if(i.frameInfos&&i.frameInfos.length>0)for(let e=i.frameInfos.length-1;e>=0;e--){const t=i.frameInfos[e];a.x+=t.bounding.x,a.y+=t.bounding.y}try{let e=await chrome.scripting.executeScript({target:{tabId:t.id},func:()=>({innerHeight:window.innerHeight||document.documentElement.clientHeight,innerWidth:window.innerWidth||document.documentElement.clientWidth})});return a.x>=0&&a.y>=0&&a.x+a.width<=e[0].result.innerWidth&&a.y+a.height<=e[0].result.innerHeight}catch{return!1}}async onGetElementRect(e){const{tab:t,target:n,newMessage:i}=await x(e);let r=await w(n,i.name,i);if(null===r||null!=r.error)return r;let a=r.result;if(i.frameInfos&&i.frameInfos.length>0)for(let e=i.frameInfos.length-1;e>=0;e--){const t=i.frameInfos[e];Array.isArray(a)&&a.length>0&&a.forEach((e=>{e.x+=t.bounding.x,e.y+=t.bounding.y}))}return r}async onFindElementsByXpath(e){const{tab:t,target:n,newMessage:i}=await x(e,!1);if(null==n)return[];let r=await w(n,i.name,i);if(null===r)return[];if(null!=r.error)return r;if(i.frameInfos&&i.frameInfos.length>0)for(let e=i.frameInfos.length-1;e>=0;e--){const t=i.frameInfos[e];Array.isArray(r.result)&&r.result.length>0&&r.result.forEach((e=>{e.boundingRectangle.x+=t.bounding.x,e.boundingRectangle.y+=t.bounding.y}))}return r}async onFindSingleElementByXpath(e){const{tab:t,target:n,newMessage:i}=await x(e,!1);if(null==n)return null;let r=await w(n,i.name,i);if(null===r||null!=r.error)return r;if(i.frameInfos&&i.frameInfos.length>0)for(let e=i.frameInfos.length-1;e>=0;e--){const t=i.frameInfos[e];r.result&&(r.result.boundingRectangle.x+=t.bounding.x,r.result.boundingRectangle.y+=t.bounding.y)}return r}async onGetHoveringElement(e){const{tab:t,target:n,newMessage:i}=await x(e);let r=await w(n,i.name,i);if(null===r||null!=r.error||null==r.result)return r;let a=r.result.bounding;if(null!==i.frameInfos&&i.frameInfos.length>0)for(let e=i.frameInfos.length-1;e>=0;e--){const t=i.frameInfos[e];a.x+=t.bounding.x,a.y+=t.bounding.y}return r}async onIsVisibleElement(e){try{const{tab:t,target:n,newMessage:i}=await x(e,!1);if(null==n)return!1;return await w(n,i.name,i)??!1}catch(e){return!1}}async onQueryElementCountByXpath(e){try{const{tab:t,target:n,newMessage:i}=await x(e,!1);if(null==n)return{count:0,tag:""};return await w(n,i.name,i)??{count:0,tag:""}}catch(e){return{count:0,tag:""}}}async onGetElementObjects(e){const{tab:t,target:n,newMessage:i}=await x(e,!1);if(null==n)return[];const r=await w(n,i.name,i);if(null===r)return[];if(null!=r.error)return r;if(null==i.frameInfos||0==i.frameInfos.length)return r;let a="";for(let e=0;e<i.frameInfos.length;e++){a+=i.frameInfos[e].xpath}const s=r.result;if(""!=a&&Array.isArray(s)&&s.length>0)for(let e=0;e<s.length;e++)s[e].iframeXPath=a;return r}async onSelectSimilarFirstElement(e){const{tab:t,target:n,newMessage:i}=await x(e);let r=await w(n,"SelectElementRequest",i);if(null===r||null!=r.error)return r;let a=r.result.element;return a.pageIcon=t.favIconUrl,this._setFrameInfoToElement(i.frameInfos,a),r=await w(n,"GenerateSimilarPreselectedRequest",i),null===r||null!=r.error?r:(this.lastSimiliarSelectionTarget.push(n),a)}async onSelectElement(e){const{tab:t,target:n,newMessage:i}=await x(e);this.onClearSimiliarElementSelectionStateRequest(e);let r=await w(n,i.name,i);if(null===r||null!=r.error)return r;let a=r.result.element;return a.pageIcon=t.favIconUrl,this._setFrameInfoToElement(i.frameInfos,a),a}_setFrameInfoToElement(e,t){const n=[];let i="";if(null!==e&&e.length>0)for(let t=0;t<e.length;t++){const r=e[t];n.unshift(...r.nodes),i+=r.xpath}t.iframeXPath=i,t.iframeNodes=n.length>0?n:null}async onSelectSimilarElement(e){const{tab:t,target:n,newMessage:i}=await x(e);let r=await w(n,i.name,i);if(null==r||null!=r.error)return r;let a=r.result;return null==a?(this.lastSimiliarSelectionTarget.push(n),a):(a.pageIcon=t.favIconUrl,this._setFrameInfoToElement(i.frameInfos,a),a)}async getFrameByIndex(e,t){return(await chrome.webNavigation.getAllFrames({tabId:e}))[t+1]}static async getPageScrollSize(e){const t=new i({tabId:e});return[(await t.sendCommand(n.Runtime_evaluate,{expression:"document.documentElement.scrollLeft"})).result.value,(await t.sendCommand(n.Runtime_evaluate,{expression:"document.documentElement.scrollTop"})).result.value]}static async getDevicePixelRatio(e){const t=new i({tabId:e});return[(await t.sendCommand(n.Runtime_evaluate,{expression:"window.devicePixelRatio"})).result.value]}async onStopLoading(e){const t=e.parameters.tabId,n=await chrome.tabs.get(t);if(v(n.url))await chrome.tabs.update(t,{url:n.url});else{var r=new i({tabId:t});await r.sendCommand("Page.stopLoading")}}}const x=async function(e,t=!0){const n=await E.getTabByMessage(e.parameters);let i={tabId:n.id,allFrames:!1,frameIds:[d.MainFrame]},r=e,a=null;if(f.IsPointRequest(e.name)){const t={x:e.parameters.x,y:e.parameters.y};a=await p.findFrameByPoint(n,t);const s=a.length>0?a[a.length-1].frame.id:d.MainFrame;i.frameIds=[s];let l=t;for(let e=0;e<a.length;e++){const t=a[e];l.x-=t.bounding.x,l.y-=t.bounding.y}r.parameters.x=l.x,r.parameters.y=l.y,r.frameInfos=a}else{(function(e){if(!e)return;const t=(e,t,n)=>{void 0!==e[t]&&void 0===e[n]&&(e[n]=e[t],delete e[t])};e.selector&&(t(e.selector,"iframeXPath","iframeXpath"),t(e.selector,"XPath","xpath"),t(e.selector,"xPath","xpath"));e.fields&&e.fields.forEach((e=>{t(e,"XPath","xpath"),t(e,"xPath","xpath"),e.elementConfig&&(t(e.elementConfig,"XPath","xpath"),t(e.elementConfig,"xPath","xpath"),t(e,"iframeXPath","iframeXpath"))}));t(e,"iframeXPath","iframeXpath"),t(e,"XPath","xpath"),t(e,"xPath","xpath"),t(e,"similarLoopXPath","similarLoopXpath")})(e.parameters);const l=(s=e.parameters)&&void 0!==s.selector&&null!==s.selector&&void 0!==s.selector.iframeXpath&&null!==s.selector.iframeXpath&&""!==s.selector.iframeXpath&&"/"!==s.selector.iframeXpath&&"\\"!==s.selector.iframeXpath?e.parameters.selector.iframeXpath:function(e){return e&&void 0!==e.iframeXpath&&null!==e.iframeXpath&&""!==e.iframeXpath&&"/"!==e.iframeXpath&&"\\"!==e.iframeXpath}(e.parameters)?e.parameters.iframeXpath:null;if(null!==l&&""!==l){if(a=await p.findFrameByIframeXPath(n,l,t),!(a.length>0))return{tab:n,target:null,newMessage:r};i.frameIds=[a[a.length-1].frame.id]}r.frameInfos=a}var s;return{tab:n,target:i,newMessage:r}},w=async function(e,t,n){try{let i=await chrome.scripting.executeScript({target:e,func:y,args:[t,n]});if(void 0!==i&&i.length>0)return null==i[0].result?null:i[0].result}catch(e){if(-1===e.message.indexOf("is showing error page")&&-1==e.message.indexOf("No frame with id")&&-1===e.message.indexOf("Cannot access chrome:// and edge:// URLs")&&-1===e.message.indexOf("Cannot access a chrome-extension:// URL")&&-1===e.message.indexOf("Cannot access a chrome:// URL")&&-1===e.message.indexOf("The extensions gallery cannot be scripted"))throw e}return null},y=async function(e,t){class n{constructor(e,t,n){this.result=t,this.error=n,this.requestId=e.requestId,this.name=e.name}}class i{constructor(e,t,n,i){this.name=e,this.code=t,this.message=n,this.stack=i}}const r={ClickElementRequest:R.clickElement,VerifyElementsRequest:R.verifyElements,QueryElementCountByXpathRequest:X.queryElementByXPathSelector,IsVisibleElementRequest:R.isVisibleElement,HoverElementRequest:R.hoverElement,FocusElementRequest:R.focusElement,GetElementObjectsRequest:R.getElementObjects,ScrollToElementRequest:X.scrollToElement,ScrollRequest:X.scroll,GetScrollBarInfoRequest:X.getScrollBarInfo,GetElementScrollBarInfoRequest:X.getElementScrollBarInfo,GetSourceRequest:X.getSource,GetContentRequest:X.getContent,GetElementAttributeRequest:R.getElementAttribute,SelectElementRequest:R.selectElement,SetCheckBoxRequest:R.setCheckBox,SetElementStyleRequest:R.setElementStyle,GetElementStyleRequest:R.getElementStyle,GetImageElementOriginBase64Request:R.getImageElementOriginBase64,GetElementInfoRequest:R.getElementInfo,GetSelectElementOptionsRequest:R.getSelectElementOptions,SetSelectElementOptionRequest:R.setSelectElementOption,EnterTextRequest:R.enterText,IsElementOnViewportRequest:R.isElementOnViewport,GetElementRectRequest:R.getElementRect,GetHoveringElementRequest:X.getHoveringElement,SelectSimilarElementRequest:R.selectSimilarElement,ClearSimilarSelectRequest:X.clearSimilarSelect,StateMachineSelectedRequest:F.selected,StateMachineHighlightRequest:F.highlight,StateMachineHighlightV2Request:F.highlight,StateMachineResetRequest:F.reset,StateMachineLoadRequest:F.load,StateMachineDeleteFieldsRequest:F.delete,StateMachineAddFieldRequest:F.add,StateMachineUpdateFieldRequest:F.update,StateMachineSetFieldOrderRequest:F.setOrder,StateMachineVerifyRequest:F.verify,StateMachineCanLoadRequest:F.canLoad,StateMachineSwitchModeRequest:F.switchMode,DataExtractorGetDataRequest:O.getData,DataExtractorResetRequest:O.reset,FindElementsByXpathRequest:R.findElementsByXpath,FindSingleElementByXpathRequest:R.findSingleElementByXpath,SetElementAttributeRequest:R.setAttribute,GenerateSimilarPreselectedRequest:R.generateSimilarPreselected,FindFrameElementFromPointRequest:L.findFrameElementFromPoint,FindFramesByXPathRequest:L.findFramesByXPath,SetFrameIdRequest:L.setFrameId,InitializeFrameIndexRequest:L.initializeFrameIndex};if(null==r[e])return new n(t,0,new i("CodeError","HandlerNotFound","Please update the version"));try{var a=t.parameters;let i=await r[e](a);return null==i&&(i=null),new n(t,i)}catch(e){return new n(t,0,new i(e.name,null!=e.errorCode&&null!==e.errorCode?e.errorCode:"JsScriptError",e.message,e.stack))}},b=function(e){return new Promise((function(t){setTimeout(t,1e3*e)}))};async function N(e,t=5,n=1){let i=0;for(;;)try{return void 0!==e.then&&"function"==typeof e.then?await e():e()}catch(e){if(i>=t||!(e?.message??"").includes("Tabs cannot be edited"))throw e;i++,await b(n)}}const v=function(e){return null==e||(e.startsWith("chrome://")||e.startsWith("edge://")||e.startsWith("about:")||e.startsWith("extension://")||e.startsWith("chrome-extension://"))},S=function(){try{chrome.tabs.query({},(function(e){e.forEach((e=>{v(e.url)||chrome.tabs.reload(e.id,{})}))}))}catch(e){return}};chrome.runtime.onInstalled.addListener((async({reason:e})=>{S(),"install"===e&&await chrome.alarms.create("heartbeat",{periodInMinutes:1})})),chrome.alarms.onAlarm.addListener((e=>{chrome.runtime.getPlatformInfo()}));const T=()=>{setInterval((()=>{chrome.runtime.getPlatformInfo()}),2e4)};setInterval((()=>{chrome.runtime.getPlatformInfo()}),2e4);const P=0!==globalThis.mozInnerScreenX,I=new g;I.connect();const C=new E(I);S();