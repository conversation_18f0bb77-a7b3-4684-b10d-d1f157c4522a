class R{static clickElement=function(e){D.clickElement(e.selector,e.clickOptions)};static verifyElements=function(e){return D.verifyElements(e.selector)};static getElementObjects=function(e){return D.getElementObjects(e.selector)};static getElementAttribute=function(e){let t=e.attributeName,n="";return n="value"==t?D.getElementValue(e.selector):"text"==t?D.getElementText(e.selector):"innerText"==t?D.getElementInnerText(e.selector):"sourceCode"==t?D.getElementOuterHtml(e.selector):"srcOrHref"==t?D.getElementSrcOrHref(e.selector):"innerHTML"==t?D.getElementInnerHtml(e.selector):D.getElementAttribute(e.selector,t),H.base64Encode(n)};static setCheckBox=function(e){D.setCheckBox(e.selector,e.operation)};static getElementStyle=function(e){return D.getElementStyle(e.selector)};static setElementStyle=function(e){D.setElementStyle(e.selector,e.value)};static getElementInfo=function(e){return D.getElementInfo(e.selector)};static getImageElementOriginBase64=async function(e){return await D.getImageElementOriginBase64(e.selector,e.removePrefix)};static getSelectElementOptions=function(e){return D.getSelectElementOptions(e.selector,e.optionsToGet)};static setSelectElementOption=function(e){D.setSelectElementOption(e.selector,e.position)};static isVisibleElement=function(e){return D.isVisibleElement(e.selector,e.checkObscured)};static hoverElement=function(e){return D.hoverElement(e.selector)};static focusElement=function(e){return D.focusElement(e.selector)};static enterText=function(e){return D.enterText(e.selector,e.value,e.options)};static isElementOnViewport=function(e){const t=D.getElement(e.selector);return D.isElementOnViewport(t)};static getElementRect=function(e){return D.getElementRects(e.selector)};static selectSimilarElement=function(e){let{shadowRoot:t,element:n}=gt.getShadowRootElementByPoint(e.x,e.y);return null===U?$.selectFirstSimilarElement(n,t):$.selectSecondSimilarElement(n,t)};static selectElement=function(e){let{shadowRoot:t,element:n}=gt.getShadowRootElementByPoint(e.x,e.y),i=null;if(n){let e=$.getSelectedElementInfo(n,G.getXPath(n),t);if(G.matchElementType(n,"IFRAME","FRAME")){const e=G.getFrameOffset(n);var r=G.getFrameIndex(n);const t=G.fromDOMRect(n.getBoundingClientRect());i={index:r,x:t.x,y:t.y,offset:e}}return{element:e,frame:i}}return{element:null,frame:i}};static findElementsByXpath=function(e){var{selector:t}=e;let n=null,i=D.getElements(t);t.xpath.indexOf("/shadow-root")>-1&&(n=gt.getShadowRootElement(t.xpath));var r=[];return i.forEach(((e,t)=>{const i=e instanceof HTMLInputElement?e.value:G.getElementText(e),a=e.tagName?.toLowerCase()??"",s=e.getAttributeNames().reduce(((t,n)=>({...t,[n]:D.getElementAttributeStringValue(e,n)??""})),{}),l=D.getOctopusElementId(e),o=D.isElementVisible(e),c=G.fromDOMRect(e.getBoundingClientRect()),u=G.getXPath(e);r.push({uid:l,tag:a,isVisible:o,xpath:null!=n?n.shadowRoot.xpath+u:u,text:H.base64Encode(i),attributes:s,boundingRectangle:c})})),r};static findSingleElementByXpath=function(e){var{selector:t}=e;let n=null,i=D.getElement(t);if(t.xpath.indexOf("/shadow-root")>-1&&(n=gt.getShadowRootElement(t.xpath)),i){const e=i instanceof HTMLInputElement?i.value:G.getElementText(i),n=i.tagName?.toLowerCase()??"",r=i.getAttributeNames().reduce(((e,t)=>({...e,[t]:D.getElementAttributeStringValue(i,t)})),{}),a=D.getOctopusElementId(i),s=D.isElementVisible(i),l=G.fromDOMRect(i.getBoundingClientRect());return{uid:a,tag:n,isVisible:s,xpath:t.xpath,text:H.base64Encode(e),attributes:r,boundingRectangle:l}}return null};static generateSimilarPreselected=function(e){let{shadowRoot:t,element:n}=gt.getShadowRootElementByPoint(e.x,e.y);return $.selectFirstSimilarElement(n,t)};static setAttribute=function(e){var{selector:t,name:n,value:i}=e;let r=D.getElement(t);r&&r.setAttribute(n,i)};static prepareBeforeCaptureElement=function(e){var{selector:t}=e,n=G.selectSingleElement(t.xpath,document,null);n&&(D.hidePageScrollBars(document),n.ownerDocument.isEqualNode(document)||D.hidePageScrollBars(n.ownerDocument),n.scrollIntoView({block:"center",inline:"nearest"}))};static recoverAfterCaptureElement=function(e){var{selector:t}=e,n=G.selectSingleElement(t.xpath,document,null);n&&(D.recoverPageScrollBars(document),n.ownerDocument.isEqualNode(document)||D.recoverPageScrollBars(n.ownerDocument))}}class X{static queryElementByXPathSelector=function(e){return D.queryElementSelector(e.selector)};static scrollToElement=function(e){let t=D.getElement(e.selector);if(null==t)return;const n=t.ownerDocument.documentElement,i=n.style.cssText;n.style.scrollBehavior="auto";let r="instant";void 0!==e.behavior&&(r=e.behavior.toLowerCase()),t.scrollIntoView({behavior:r,block:"center",inline:"nearest"}),n.style.cssText=i};static scroll=function(e){D.scroll(e)};static getSource=function(e){let t=D.getPageSource();return H.base64Encode(t)};static getContent=function(e){let t=D.getPageContent();return H.base64Encode(t)};static getHoveringElement=function(e){let t=G.getElementFromPoint(e.x,e.y);if(t){const e=G.getElementTagName(t),n=G.fromDOMRect(t.getBoundingClientRect());let{x:i,y:r,width:a,height:s}=n;return n.x=Math.max(0,i),n.y=Math.max(0,r),n.height=Math.min(r+s,window.innerHeight)-Math.max(0,r),n.width=Math.min(i+a,window.innerWidth)-Math.max(0,i),{bounding:n,tag:e}}return null};static getScrollBarInfo=function(e){return D.getScrollBarInfo()};static getElementScrollBarInfo=function(e){return D.getElementScrollBarInfo(e.selector)};static clearSimilarSelect=function(e){return $.clearSimilarSelect()}}class F{static selected=function(e){var{xpath:t,doc:n}=he(e.selector.xpath);return be.selected(t,n)};static load=function(e){var{fields:t,similarLoopXpath:n,state:i}=e,{xpath:r,doc:a}=he(n);return be.load(t,r,i,a)};static highlight=function(e){var{highlightFields:t,similarLoopXpath:n}=e,{doc:i,similarLoopXpath:n}=F.converterSimilarLoopXPathByShadowRoot(n);return be.highlight(t,n,i)};static switchMode=function(e){const{mode:t}=e;return null!=me?be.reset(t):null};static reset=function(e){null!=me&&(be.reset(),me=null)};static delete=function(e){const{nameList:t}=e;return be.delete(t)};static add=function(e){const{field:t,name:n}=e;return be.add(t,n)};static update=function(e){const{field:t,name:n}=e;return be.update(t,n)};static setOrder=function(e){const{nameList:t}=e;return be.setOrder(t)};static canLoad=function(e){var{fields:t,similarLoopXpath:n}=e,{doc:i,similarLoopXpath:n}=F.converterSimilarLoopXPathByShadowRoot(n);return be.canLoad(t,n,i)};static verify=function(e){var{field:t,similarLoopXpath:n}=e;let i=G.initialLower(t.type);var{doc:r,similarLoopXpath:n}=F.converterSimilarLoopXPathByShadowRoot(n);return"element"==i?be.verify(t.elementConfig.xpath,t.elementConfig.ignoreSimilarLoop,n,r):0};static converterSimilarLoopXPathByShadowRoot=function(e){var t=document;if(!H.isNullOrEmpty(e)&&e.indexOf("/shadow-root")>-1){var n=gt.getShadowRootElement(e);if(null==n)return{doc:t,similarLoopXpath:e};t=n.shadowRoot.doc,e=n.xpath}return{doc:t,similarLoopXpath:e}}}class O{static getData=function(e){var{fields:t,similarLoopXpath:n,onlyOnScreen:i}=e,{doc:r,similarLoopXpath:n}=F.converterSimilarLoopXPathByShadowRoot(n);return He.getData(t,n,i,r)};static reset=function(e){return He.reset()}}class L{static isFrameElement=function(e){return e instanceof HTMLFrameElement||e instanceof HTMLIFrameElement};static filterChildFrames=function(e,t){return e.filter((e=>e.parentFrameId===t))};static findFrameElementFromPoint=function(e){const{point:t,parentFrameId:n,allFrames:i}=e,{shadowRoot:r,element:a}=gt.getShadowRootElementByPoint(t.x,t.y),s=a;if(!L.isFrameElement(s))return{frameInfo:null,hasChildren:!1};const l=L.filterChildFrames(i,n),o=G.getXPath(s),c=$.getSelectedElementInfo(s,o,r),u=G.getFrameIdByFrameElement(s,l);if(!u)return{frameInfo:null,hasChildren:!1};c.frame=u;const m=D.getElementOffset({xpath:o}),h=G.fromDOMRect(s.getBoundingClientRect());c.bounding={x:h.x+m.x,y:h.y+m.y,width:h.width,height:h.height};const d=i.some((e=>e.parentFrameId===c.frame.id));return{frameInfo:c,hasChildren:d}};static findFramesByXPath=function(e){const{iframeXpath:t,parentFrameId:n,allFrames:i}=e,r=L.filterChildFrames(i,n),a=[],s=G.selectElements(t,document,null);let l=null;if(t.indexOf("/shadow-root")>-1){if(shadowRootElement=gt.getShadowRootElement(t),null==shadowRootElement)return a;l=shadowRootElement.shadowRoot}return s.forEach((e=>{if(!L.isFrameElement(e))return;const t=G.getXPath(e),n=$.getSelectedElementInfo(e,t,l),i=G.getFrameIdByFrameElement(e,r);if(!i)return;n.frame=i;const s=D.getElementOffset({xpath:t}),o=G.fromDOMRect(e.getBoundingClientRect());n.bounding={x:o.x+s.x,y:o.y+s.y,width:o.width,height:o.height},a.push(n)})),a};static setFrameId=function(e){var{frameId:t}=e;L.octFrameId=t;try{var n=L.findAllFrames(window.parent.document);for(const e of n)if(e.contentWindow===window){e.setAttribute("oct-frame-id",t);break}}catch(e){if(0==L.octFrameIndex)return;window.parent.postMessage({type:"SET_OCT_FRAME_ID",frameId:t,frameIndex:L.octFrameIndex},"*")}};static initializeFrameIndex=function(e){L.findAllFrames().forEach(((e,t)=>{L.octFrameCount+=1,e.setAttribute("oct-frame-index",L.octFrameCount.toString()),e.contentWindow.postMessage({type:"SET_OCT_FRAME_INDEX",frameIndex:L.octFrameCount},"*")}))};static findFrameByXpath=function(e){var{iframeXpath:t}=e,n=G.selectSingleElement(t,document,null);if(L.isFrameElement(n)){const e=G.getFrameOffset(n),t=G.fromDOMRect(n.getBoundingClientRect());return{frameId:n.getAttribute("oct-frame-id"),rect:t,offset:e}}return null};static findFrameByPoint=function(e){var{x:t,y:n}=e;let i=G.getElementFromPoint(t,n);if(L.isFrameElement(i)){const e=G.getFrameOffset(i),t=G.fromDOMRect(i.getBoundingClientRect()),n=i.getAttribute("oct-frame-id"),r=G.getXPath(i);return{frameId:n,rect:t,offset:e,nodes:G.getNodes(i),xpath:r}}return null};static findAllFrames(e=document){const t=[];var n=e.querySelectorAll("iframe, frame");t.push(...n);const i=e=>{if(!e)return;if(e.shadowRoot){var n=e.shadowRoot.querySelectorAll("iframe, frame");t.push(...n)}const r=e.children||[];for(const e of r)i(e)};return i(e),t}}L.octFrameId=0,L.octFrameIndex=0,L.octFrameCount=0,window.addEventListener("message",(e=>{if("SET_OCT_FRAME_INDEX"===e.data.type&&0!=e.data.frameIndex&&(L.octFrameIndex=e.data.frameIndex,0!=L.octFrameId&&0!=L.octFrameIndex&&L.setFrameId({frameId:L.octFrameId})),"SET_OCT_FRAME_ID"===e.data.type){var t=L.findAllFrames().find((t=>t.getAttribute("oct-frame-index")===e.data.frameIndex.toString()));t&&t.setAttribute("oct-frame-id",e.data.frameId)}}));class D{static clickElement=function(e,t){const n=D.getElement(e);if(n){n.focus();let{ctrlKey:e,altKey:i,shiftKey:r,buttonType:a,isDoubleClick:s}=t;const l=new MouseEvent(s?"dbclick":"click",{bubbles:!0,cancelable:!0,view:window,ctrlKey:e,altKey:i,shiftKey:r,buttonType:a});n.dispatchEvent(l)}};static verifyElements=function(e){const t=D.getElements(e);return we.flicker(t),t.length};static hoverElement=function(e){const t=D.getElement(e);if(t){const e=t.getBoundingClientRect();if(e.top>=0&&e.left>=0&&e.right<=(window.innerWidth||document.documentElement.clientWidth)&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)){const e=new MouseEvent("mouseover",{bubbles:!0,cancelable:!0,view:window});t.dispatchEvent(e)}}};static focusElement=function(e){const t=D.getElement(e);if(t){const e=new FocusEvent("focus");t.dispatchEvent(e)}};static getSelectElementOptions=function(e,t){const n=D.getElement(e);if(n&&(n instanceof HTMLSelectElement||"HTMLSelectElement"===n.constructor.name)){const{options:e,selectedIndex:i}=n;return"all"===t.toLowerCase()?[...e].map((e=>e.text)):e[i]?[e[i].text]:[]}return[]};static setSelectElementOption=function(e,t){const n=D.getElement(e);if(n&&(n instanceof HTMLSelectElement||"HTMLSelectElement"===n.constructor.name)){let e=n.options;if(t>=0&&t<e.length){e[t].selected=!0;const i=new Event("change",{bubbles:!0});n.dispatchEvent(i)}}};static setCheckBox=function(e,t){const n=D.getElement(e);if(n&&n instanceof HTMLInputElement&&"checkbox"==n.type){const e=t.toLowerCase();if("reverse"==e||n.checked&&"uncheck"==e||!n.checked&&"check"==e){const e=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window});n.dispatchEvent(e)}}};static enterText=function(e,t,n){const i=D.getElement(e);if(i&&(i instanceof HTMLInputElement||i instanceof HTMLTextAreaElement)){let{isAppend:e}=n;i.focus(),i.value=e?i.value+t:t}};static queryElementSelector=function(e){const t=D.getElements(e),n=t.length>0?t[0].tagName:"";return{count:t.length,tag:n}};static isElementExists=function(e){return!!D.getElement(e)};static getElementAttributeStringValue(e,t){if(null==e)return"";return["innerHTML","innerText","textContent","outerHTML","value","src","href"].includes(t)?e[t]??"":e.getAttribute(t)??""}static getElementProp=function(e,t){const n=D.getElement(e);return n?n[t]??"":""};static getElementAttribute=function(e,t){const n=D.getElement(e);return n?n.getAttribute(t):""};static getElementText=function(e){const t=D.getElement(e);let n="";if(t)if(t instanceof HTMLInputElement)n=t.value??"";else{const e=t.innerText??"",i=t.textContent??"";n=e.length>=i.length?e:i}return n};static getElementInnerText=function(e){const t=D.getElement(e);let n="";if(t)if(t instanceof HTMLInputElement)n=t.value??"";else{n=t.innerText??""}return n};static getElementInnerHtml=function(e){return D.getElementProp(e,"innerHTML")};static getElementOuterHtml=function(e){return D.getElementProp(e,"outerHTML")};static getElementValue=function(e){return D.getElementProp(e,"value")};static isVisibleElement=function(e,t=!1){const n=D.getElement(e);if(!n)return!1;let i=G.isVisibleElement(n);return i&&t?!D.isElementBeObscured(n):i};static getElementSrcOrHref=function(e){const t=D.getElementProp(e,"src"),n=D.getElementProp(e,"href");return n.length>=t.length?n:t};static scrollToElement=function(e){let t=D.getElement(e.selector);if(null==t)return;const n=t.ownerDocument.documentElement,i=n.style.cssText;n.style.scrollBehavior="auto";let r="instant";void 0!==e.behavior&&(r=e.behavior.toLowerCase()),t.scrollIntoView({behavior:r,block:"center",inline:"nearest"}),n.style.cssText=i};static scroll=function(e){let t=null,n=e.behavior,i=e.scrollTo.toLowerCase();if(t=null!=e.selector&&null!=e.selector?D.getElement(e.selector):window,null==t)return;null!=e.lookupIfNonScrollable&&e.lookupIfNonScrollable&&(t=G.getScrollableAncestor(t,"vertical"));let r=0;if("top"==i)r=0;else if("bottom"==i)r=t==window?document.body.scrollHeight:t.scrollHeight;else{if("onescreen"!=i)return;r=t==window?document.documentElement.scrollTop+document.documentElement.clientHeight:t.scrollTop+t.clientHeight}t.scroll({top:r,left:0,behavior:n})};static isElementOnViewport=function(e){if(!e)return!1;const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)};static isElementPartiallyInViewport=function(e){if(!e)return!1;const t=e.getBoundingClientRect();return t.bottom>0&&t.right>0&&t.top<(window.innerHeight||document.documentElement.clientHeight)&&t.left<(window.innerWidth||document.documentElement.clientWidth)};static isElementVisible=function(e){return!!e&&G.isVisibleElement(e)};static isElementBeObscured=function(e){const t=e.getBoundingClientRect(),n=G.getElementFromPoint(t.left+t.width/2,t.top+t.height/2);return e!=n&&!e.contains(n)};static getElementRect=function(e){let t={x:0,y:0,width:0,height:0};const n=D.getElement(e);if(n){const e=n.getBoundingClientRect();t={x:e.x,y:e.y,width:e.width,height:e.height}}return t};static getElementRects=function(e){const t=D.getElements(e);var n=[];return t.forEach(((e,t)=>{const i=e.getBoundingClientRect();let r={x:i.x,y:i.y,width:i.width,height:i.height};n.push(r)})),n};static getElementOffset=function(e){const t=D.getElement(e),n=parseInt(window.getComputedStyle(t,null).getPropertyValue("padding-left"))||0,i=parseInt(window.getComputedStyle(t,null).getPropertyValue("padding-top"))||0;return{x:n+(parseInt(window.getComputedStyle(t,null).getPropertyValue("border-left-width"))||0),y:i+(parseInt(window.getComputedStyle(t,null).getPropertyValue("border-top-width"))||0)}};static getElementObjects=function(e){const t=D.getElements(e),n=[];return t.forEach(((t,i)=>{const r=t instanceof HTMLInputElement?t.value:t.textContent,a=`(${e.xpath})[${i+1}]`,s=t instanceof HTMLImageElement?t.src:t.href,l=t.tagName;let o={uid:D.getOctopusElementId(t),xpath:a,iframeXPath:e.iframeXpath??"",text:H.base64Encode(r),link:s??"",tag:l??""};o.link=String(s),n.push(o)})),n};static getPageSource=function(){return document.documentElement.outerHTML};static getPageContent=function(){return document.documentElement.innerText};static getPageRect=function(){let e=document.documentElement.getBoundingClientRect();return{x:e.left,y:e.top,width:e.width,height:e.height}};static hidePageScrollBars=function(e){const t=e.documentElement;t&&(A.set(t,t.style.cssText),t.style.overflow="hidden",t.style.scrollBehavior="auto")};static recoverPageScrollBars=function(e){const t=e.documentElement;t&&A.has(t)&&(t.style.cssText=A.get(t),A.delete(t))};static getElementStyle=function(e){let t=D.getElement(e);if(null!=t)return t.style.cssText};static setElementStyle=function(e,t){let n=D.getElement(e);null!=n&&(n.style.cssText=t)};static getElementInfo=function(e){let t=D.getElements(e);if(!t||0===t.length)return[];const n=[];return t.forEach((e=>{const t=G.fromDOMRect(e.getBoundingClientRect()),i={x:t.x,y:t.y,width:t.width,height:t.height},r=e.tagName?.toLowerCase()??"",a=G.getXPath(e);n.push({tag:r,bounding:i,xpath:a})})),n};static getImageElementOriginBase64=async function(e,t){let n=D.getElement(e);if(!n||"img"!==n.tagName?.toLowerCase())return"";const i=n.src;try{const e=await fetch(i),n=await e.blob();return await new Promise(((e,i)=>{const r=new FileReader;r.onloadend=()=>{const n=t?r.result.replace(/^data:image\/\w+;base64,/,""):r.result;e(n)},r.onerror=()=>i(""),r.readAsDataURL(n)}))}catch{return""}};static prepareBeforeCaptureElement=function(e){var t=D.getElement(e);t&&(D.hidePageScrollBars(document),t.ownerDocument.isEqualNode(document)||D.hidePageScrollBars(t.ownerDocument),t.scrollIntoView({block:"center",inline:"nearest"}))};static recoverAfterCaptureElement=function(e){D.recoverPageScrollBars(document);var t=D.getElement(e);t&&(t.ownerDocument.isEqualNode(document)||D.recoverPageScrollBars(t.ownerDocument))};static getScrollBarInfo=function(){return{scrollTop:document.documentElement.scrollTop||document.body.scrollTop,clientHeight:document.documentElement.clientHeight||document.body.clientHeight,scrollHeight:document.documentElement.scrollHeight||document.body.scrollHeight}};static getElementScrollBarInfo=function(e){let t=null;if(null==e||null==e)return{scrollTop:-1,clientHeight:-1,scrollHeight:-1};t=D.getElement(e),t=G.getScrollableAncestor(t,"vertical");return{scrollTop:t.scrollTop,clientHeight:t.clientHeight,scrollHeight:t.scrollHeight}};static generateUID=function(){var e=46656*Math.random()|0,t=46656*Math.random()|0;return(e=("000"+e.toString(36)).slice(-3))+(t=("000"+t.toString(36)).slice(-3))};static getOctopusElementId=function(e){let t=e.getAttribute(D.elementIdTagProperty);if(t)return t;{let t=D.generateUID();return e.setAttribute(D.elementIdTagProperty,t),t}};static getElement=function(e){const{xpath:t,iframeXpath:n}=e;return!D.ignoreIframe&&n?(D.scrollToElement({xpath:n}),G.selectSingleElement(t,document,n)):G.selectSingleElement(t,document,null)};static getElements=function(e){const{xpath:t,iframeXpath:n}=e;return!D.ignoreIframe&&n?(D.scrollToElement({xpath:n}),G.selectElements(t,document,n)):G.selectElements(t,document,null)}}let A=new Map;D.elementIdTagProperty="octopus-uid",D.ignoreIframe=!0;const k=["octopus-rpa-similar-selected","octopus-rpa-similar-preselected"],q=["oct-frame-index","oct-frame-id","data-octopus-rpa"];class M{constructor(e,t,n=!1,i="EqualTo"){this.name=e,this.value=t,this.isEnable=n,this.op=i}}class B{constructor(e,t=!1){this.name=e.tagName,this.attributes=this.getElementXAttributes(e),this.isEnable=t,e instanceof SVGElement&&(this.name="*",this.setAttributeValue("local-name()",!0,e.localName))}getElementXAttributes(e){for(var t=e.attributes,n=[],i=0;i<t.length;i++){var r=t[i].name;if("style"!=r&&"html"!=e.localName){if(q.includes(r))continue;"class"==r?n.push(new M(r,G.getClassName(e),!1)):n.push(new M(r,t[i].value,!1))}}return n.push(new M("position()",G.getElementSiblingIndex(e),!1)),n}setAttributeValue(e,t=!1,n=null,i=null){let r=this.attributes.find((t=>t.name===e));null==r?(r=new M(e,null==n?"position()"==e?1:"":n,t,null==i?"EqualTo":i),this.attributes.push(r)):(r.isEnable=t,null!=n&&(r.value=n),null!=i&&(r.op=i))}}class H{static base64Encode(e){return H.isNullOrEmpty(e)?"":(e=String.prototype.toWellFormed?e.toWellFormed():e.replace(/[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?<![\uD800-\uDBFF])[\uDC00-\uDFFF]/g,"�"),btoa(encodeURIComponent(e)))}static htmlDecode(e){let t="";return 0==e.length?"":(t=e.replace(/&gt;/g,"&"),t=t.replace(/&lt;/g,"<"),t=t.replace(/&gt;/g,">"),t=t.replace(/&nbsp;/g,"    "),t=t.replace(/'/g,"'"),t=t.replace(/&quot;/g,'"'),t=t.replace(/<br>/g,"\n"),t)}static htmlEncode(e){return 0==e.length?"":e.replace(/&/g,"&gt;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/ /g,"&nbsp;").replace(/\'/g,"'").replace(/\"/g,"&quot;").replace(/\n/g,"<br>")}static hasInt(e){return/\d+/.test(e)}static isInt(e){return/^(-){0,1}\d+$/.test(e)}static isNullOrEmpty(e){return void 0===e||!e||0===e.length}static split(e,t,n=_.None,i){var r=e.split(t,i);switch(n){case _.RemoveEmptyEntries:r=r.filter(Boolean);break;case _.RemoveEmptyOrWhitespaceEntries:r=r.filter((e=>""!=e.trim()));case _.None:}return r}static isUrl(e){return new RegExp("^(((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://)|(www\\.))+(([a-zA-Z0-9\\._-]+\\.[a-zA-Z]{2,6})|([0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}))(/[a-zA-Z0-9\\&amp;%_\\./-~-]*)?$").test(e)}static isMobile(e){return new RegExp("^1[3-9]\\d{9}$").test(e)}static isIp(e){return new RegExp("^(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9])\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[1-9]|0)\\.(25[0-5]|2[0-4][0-9]|[0-1]{1}[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])$").test(e)}static isPostCode(e){return new RegExp("^\\d{6}$").test(e)}static isTel(e){return new RegExp("^\\d{3,4}-?\\d{6,8}$").test(e)}static isEmail(e){return new RegExp("^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$").test(e)}}var _;(function(e){e[e.None=0]="None",e[e.RemoveEmptyEntries=1]="RemoveEmptyEntries",e[e.RemoveEmptyOrWhitespaceEntries=2]="RemoveEmptyOrWhitespaceEntries"})(_={});const V=["red","top","active","hot","on","hover"],W=["下一页","下一页>","下一页 >","下一页>>","下一页 >>","[下一页]","后页","后一页",">",">>","Next","next","next page","Next Page"];class G{static getIndexedXPath(e){if(!e||e.nodeType!=Node.ELEMENT_NODE)return"";if("body"===e.nodeName.toLowerCase())return"//body";if(!e.parentNode)return"//"+e.localName;for(var t=1,n=e.previousElementSibling;n;)n.nodeType==Node.ELEMENT_NODE&&e.localName===n.localName&&t++,n=n.previousElementSibling;var i=e.localName;return e instanceof SVGElement&&(i=`*[local-name()='${e.localName}']`),(e.localName.indexOf(":")>-1||e.localName.indexOf("=")>-1)&&(i=e.localName.indexOf('"')?`*[local-name()='${e.localName}']`:`*[local-name()="${e.localName}"]`),/.+\s$/.test(e.localName)&&(i=`*[contains(local-name(),"${e.localName.trim()}")]`),G.getIndexedXPath(e.parentElement)+"/"+i+"["+t+"]"}static stringFormat(...e){if(0==e.length)return"";for(var t=e[0],n=1;n<e.length;n++){var i=new RegExp("\\{"+(n-1)+"\\}","gm");t=t.replace(i,e[n])}return t}static appendCSS(e,t){G.isHTMLElement(e)&&!H.isNullOrEmpty(t)&&(e.style.cssText+=t)}static setCSS(e,t){G.isHTMLElement(e)&&(null==t&&(t=""),e.style.cssText=t)}static appendCSSByXPath(e,t){if(!H.isNullOrEmpty(e)&&!H.isNullOrEmpty(t)){var n=G.selectSingleElement(e);n&&G.appendCSS(n,t)}}static getXPathEvaluateResult(e,t){return(new XPathEvaluator).evaluate(e,t,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null)}static getIframeDocument(e,t){t||(t=document);var n=G.getXPathEvaluateResult(e,document);if(null!=n){let e=n.iterateNext();if(e instanceof HTMLIFrameElement){return e.contentWindow.document}if(e instanceof HTMLFrameElement){return e.contentWindow.document}}return null}static selectElements(e,t,n){if(t||(t=document),e.indexOf("/shadow-root")>-1||t instanceof DocumentFragment)return gt.selectElements(e,t);if(!H.isNullOrEmpty(n)){let e=G.getIframeDocument(n,document);if(!e)return null;t=e}var i=G.getXPathEvaluateResult(e,t),r=new Array;if(null!=i)for(var a=i.iterateNext();a;)a.nodeType==Node.ELEMENT_NODE&&r.push(a),a=i.iterateNext();return r}static selectSingleElement(e,t,n){if(t||(t=document),e.indexOf("/shadow-root")>-1||t instanceof DocumentFragment)return gt.selectSingleElement(e,t);if(!H.isNullOrEmpty(n)){let e=G.getIframeDocument(n,t);if(!e)return null;t=e}var i=G.getXPathEvaluateResult(e,t);if(null!=i){let e=i.iterateNext();return G.isHTMLElement(e)?e:null}return null}static getXPath2(e){let t=[];for(var n=document.getElementsByTagName("*");e&&e.nodeType==Node.ELEMENT_NODE;e=e.parentElement){var i=this.getClassName(e);if(e.hasAttribute("id")){for(var r=0,a=0;a<n.length&&(n[a].hasAttribute("id")&&n[a].id==e.id&&r++,!(r>1));a++);if(1==r)return t.unshift('id("'+e.getAttribute("id")+'")'),t.join("/");t.unshift(e.localName.toLowerCase()+'[@id="'+e.getAttribute("id")+'"]')}else if(H.isNullOrEmpty(i)){for(var s=1,l=e.previousElementSibling;l;l=l.previousElementSibling)l.localName==e.localName&&s++;t.unshift(e.localName.toLowerCase()+"["+s+"]")}else t.unshift(e.localName.toLowerCase()+'[@class="'+i+'"]')}return t.length?"/"+t.join("/"):null}static getXPath(e,t=!1,n=-1){if(!e)return"";var i=e.localName;if(e instanceof SVGElement&&(i=`*[local-name()='${e.localName}']`),(e.localName.indexOf(":")>-1||e.localName.indexOf("=")>-1)&&(i=e.localName.indexOf('"')?`*[local-name()='${e.localName}']`:`*[local-name()="${e.localName}"]`),/.+\s$/.test(e.localName)&&(i=`*[contains(local-name(),"${e.localName.trim()}")]`),null==e.parentElement)return("html"===e.localName.toLowerCase()?"/":"//")+e.localName;if(!H.isNullOrEmpty(e.id)&&!H.hasInt(e.id)&&"string"==typeof e.id){var r=!0;if(Array.from(document.getElementsByTagName(e.tagName)).every((t=>!(!t.isEqualNode(e)&&t.id==e.id)||(r=!1,!1))),r)return"//"+i+'[@id="'+e.id+'"]'}let a=this.getClassName(e);if(!H.isNullOrEmpty(a)&&!H.hasInt(a)){let n=!1;if(t&&"li"===e.tagName.toLowerCase())for(const e of V)if(-1!=a.indexOf(e)){n=!0;break}if(!n)if(G.hasUniqueClassNameInSameTags(e))return"//"+i+'[@class="'+a+'"]'}return 0==e.children.length&&e.textContent&&W.includes(e.textContent)?"//"+i+'[text()="'+G.firstNotEmptyTextNodeValue(e)+'"]':(-1==n&&(n=G.getElementSiblingIndex(e)),G.getXPath(e.parentElement,t)+"/"+i+"["+n+"]")}static firstNotEmptyTextNodeValue(e){for(const t of e.childNodes)if(t.nodeType===Node.TEXT_NODE&&!H.isNullOrEmpty(t.nodeValue.trim()))return t.nodeValue;return null}static getNodes(e,t=[],n=!1){if(!e)return[];var i=G.getElementSiblingIndex(e),r=new B(e);r.setAttributeValue("position()",!1,i),e instanceof SVGElement&&(r.name="*",r.setAttributeValue("local-name()",!0,e.localName));var a=G.firstNotEmptyTextNodeValue(e);if(null!=a&&r.setAttributeValue("text()",!1,a),null==e.parentElement)return r.isEnable=!n,t.push(r),t;if(n)return r.isEnable=!1,t.push(r),G.getNodes(e.parentElement,t,!0);if(!H.isNullOrEmpty(e.id)&&!H.hasInt(e.id)&&"string"==typeof e.id){var s=!0;if(Array.from(document.getElementsByTagName(e.tagName)).every((t=>!(!t.isEqualNode(e)&&t.id==e.id)||(s=!1,!1))),s)return r.isEnable=!0,r.setAttributeValue("id",!0),t.push(r),G.getNodes(e.parentElement,t,!0)}let l=this.getClassName(e);if(!H.isNullOrEmpty(l)&&!H.hasInt(l)){let n=!1;if("li"===e.tagName.toLowerCase())for(const e of V)if(-1!=l.indexOf(e)){n=!0;break}if(!n)if(G.hasUniqueClassNameInSameTags(e))return r.setAttributeValue("class",!0),r.isEnable=!0,t.push(r),G.getNodes(e.parentElement,t,!0)}return 0==e.children.length&&e.textContent&&W.includes(e.textContent)?(r.setAttributeValue("text()",!0,G.firstNotEmptyTextNodeValue(e)),r.isEnable=!0,t.push(r),G.getNodes(e.parentElement,t,!0)):(r.setAttributeValue("position()",!0),r.isEnable=!0,t.push(r),G.getNodes(e.parentElement,t,!1))}static getSimpleXPath(e){return G.getXPath(e)}static getXPathForDynamic(e,t,n=0){if(!e)return"";if(null==e.parentElement)return("html"===e.localName.toLowerCase()?"/":"//")+e.localName;if(n>=t&&!H.isNullOrEmpty(e.id)&&!H.hasInt(e.id)){var i=!0;if(Array.from(document.getElementsByTagName(e.tagName)).every((t=>!(!t.isEqualNode(e)&&t.id==e.id)||(i=!1,!1))),i)return"//"+e.tagName+'[@id="'+e.id+'"]'}if(0==e.children.length&&e.textContent&&W.includes(e.textContent))return"//"+e.tagName+'[text()="'+G.firstNotEmptyTextNodeValue(e)+'"]';var r=G.getElementSiblingIndex(e);return H.isNullOrEmpty(e.id)&&H.isNullOrEmpty(e.className)||n++,G.getXPathForDynamic(e.parentElement,t,n)+"/"+e.tagName+"["+r+"]"}static getNodesForDynamic(e,t,n=0,i=[],r=!1){if(!e)return[];var a=G.getElementSiblingIndex(e);H.isNullOrEmpty(e.id)&&H.isNullOrEmpty(e.className)||n++;var s=new B(e,!1);if(s.setAttributeValue("position()",!1,a),null==e.parentElement)return s.isEnable=!r,i.push(s),i;if(r)return i.push(s),G.getNodes(e.parentElement,i,!0);if(n>=t&&!H.isNullOrEmpty(e.id)&&!H.hasInt(e.id)){var l=!0;if(Array.from(document.getElementsByTagName(e.tagName)).every((t=>!(!t.isEqualNode(e)&&t.id==e.id)||(l=!1,!1))),l)return s.isEnable=!0,s.setAttributeValue("id",!0),i.push(s),G.getNodes(e.parentElement,i,!0)}return 0==e.children.length&&e.textContent&&W.includes(e.textContent)?(s.isEnable=!0,s.setAttributeValue("text()",!0,G.firstNotEmptyTextNodeValue(e)),i.push(s),G.getNodes(e.parentElement,i,!0)):(s.isEnable=!0,s.setAttributeValue("position()",!0),i.push(s),G.getNodesForDynamic(e.parentElement,t,n,i,r))}static hasUniqueClassNameInSameTags(e){var t=this.getClassName(e),n=Array.from(document.getElementsByClassName(t));return 0!=n.length&&n.every((t=>t.isSameNode(e)||t.nodeName!=e.tagName))}static getElementText(e){if(!e)return"";var t="";let n;for(n=e.firstChild;n;)n.nodeType===Node.TEXT_NODE&&n.textContent&&(t+=n.textContent.trim()),n=n.nextSibling;return t}static isInvalidElement(e){return!G.isVisibleElement(e)||!!(e.textContent&&e.textContent.trim().length>0&&e.innerHTML.includes("pos.baidu.com"))}static isVisibleElement(e){let t=e.currentStyle?e.currentStyle.visibility:getComputedStyle(e,null).visibility;if("collapse"==t||"hidden"==t)return!1;let n=e.getBoundingClientRect();return e.clientHeight>0&&e.clientWidth>0||e.offsetHeight>0&&e.offsetWidth>0||n.width>0&&n.height>0}static matchRegExp(e,t){return new RegExp(e,arguments[2]).exec(t)}static isHTMLElement(e){if(!e)return!1;let t=!1;try{t=e instanceof HTMLElement||e instanceof Element}catch(n){t="object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument}finally{t||(t="object"==typeof e&&1===e.nodeType&&"object"==typeof e.style&&"object"==typeof e.ownerDocument)}return t}static getShortClassXPath(e){var t="",n=H.split(e,"/",_.RemoveEmptyEntries);for(let e=n.length-1;e>=0;e--){if(n[e].includes("@class")){t="/descendant::"+n[e]+t;break}t="/"+n[e]+t}return t}static getSampledDynamicXPath(e){if(!e||e.length<2)return"";var t=!1;let n=new Array;if(e.length>120){n=e.slice(0,30);var i=Math.round(e.length/2);n.concat(e.slice(i-15,i+15)),n.concat(e.slice(e.length-30)),t=!0}else n=e;return G.getDynamicXPath(n,t)}static getDynamicXPath(e,t){var n=G.getDynamicXPathByStyle(e,t);if(H.isNullOrEmpty(n))for(let t=0;t<3&&(n=G.getDynamicXPathByPosition(e,t),H.isNullOrEmpty(n));t++);return n}static getDynamicNodes(e,t){var n=G.getDynamicNodesByStyle(e,t);if(null==n||0==n.length)for(let t=0;t<3&&!(null!=(n=G.getDynamicNodesByPosition(e,t))&&n.length>=0);t++);return n}static isEqualAtSamePosition(e,t){for(let n=1;n<e.length;n++)if(e[n][t]!=e[n-1][t])return!1;return!0}static getDynamicXPathByPosition(e,t){var n="",i=new Array,r=new Array;e.forEach((e=>{var n=G.getXPathForDynamic(e,t);i.push(n),r.push(H.split(n,/\[|\]/,_.RemoveEmptyEntries))}));var a=!0;for(let e=1;e<i.length;e++)if(r[e].length!=r[e-1].length){a=!1;break}if(a){for(let e=0;e<r[0].length;e++){if(G.isEqualAtSamePosition(r,e))n+=r[0][e];else{var s=1;for(let t=0;t<i.length;t++){if(!H.isInt(r[t][e]))return null;{let n=Number.parseInt(r[t][e]);0==t&&(s=n),s=n<s?n:s}}s>1&&(n+="position()>"+(s-1))}n+=e%2==0?"[":"]"}n=n.replace(/\[\]/g,"")}return n}static getDynamicNodesByPosition(e,t){var n=[],i=new Array;e.forEach((e=>{var n=G.getNodesForDynamic(e,t);i.push(n)}));var r=!0;for(let e=1;e<i.length;e++)if(i[e].length!=i[e-1].length){r=!1;break}if(r)for(let e=0;e<i[0].length;e++){var a=!0;for(let t=1;t<i.length;t++){if(!i[t][e].isEnable||!i[t-1][e].isEnable||i[t][e].name!=i[t-1][e].name){a=!1;break}{let n=i[t][e].attributes.find((e=>e.isEnable))||{name:"",value:""},r=i[t-1][e].attributes.find((e=>e.isEnable))||{name:"",value:""};if(n.name!=r.name||n.value!=r.value){a=!1;break}}}if(a)n.push(i[0][e]);else{var s=1;for(let t=0;t<i.length;t++){let n=i[t][e].attributes.find((e=>"position()"===e.name));if(null==n||null==n)return null;{let e=n.value;0==t&&(s=e),s=e<s?e:s}}let t=i[0][e].attributes.find((e=>"position()"===e.name));s>1?(t.value=s-1,t.op="GreaterThan",n.push(i[0][e])):(t.isEnable=!1,n.push(i[0][e]))}}return n}static getDynamicXPathByStyle(e,t){var n="",i=!0,r=new Array;for(let t=1;t<e.length;t++){var a=e[t-1]instanceof SVGElement?"":e[t-1].className,s=e[t]instanceof SVGElement?"":e[t].className;if(1==t&&(r=a.split(" ")),(H.isNullOrEmpty(a)||a!==s)&&(i=!1),H.isNullOrEmpty(s))H.isNullOrEmpty(s)&&(r.length=0);else{var l=s.split(" ");for(let e=r.length-1;e>-1;e--)l.includes(r[e])||r.splice(e,r.length-e)}if(0==r.length)break}if(i){var o=e[0];if((m=G.getElements(o.tagName,o.className))&&m.length>0&&m.length<101)n=(c=G.getElementClassSiblingIndex(o,o.className))>1?"//"+o.tagName+"[@class='"+o.className+"'][position()>"+(c-1)+"]":"//"+o.tagName+"[@class='"+o.className+"']"}else if(r.length>0&&!t){o=e[0];var c,u,m,h=r.join(" ");u=(c=G.getElementClassSiblingIndex(o,h))>1?"//"+o.tagName+"[contains(@class,'"+h+"')][position()>"+(c-1)+"]":"//"+o.tagName+"[contains(@class,'"+h+"')]",(m=G.selectElements(u))&&m.length>0&&m.length>e.length&&(n=u)}return n}static getDynamicNodesByStyle(e,t){var n=[],i=!0,r=new Array;for(let t=1;t<e.length;t++){var a=e[t-1]instanceof SVGElement?"":e[t-1].className,s=e[t]instanceof SVGElement?"":e[t].className;if(1==t&&(r=a.split(" ")),(H.isNullOrEmpty(a)||a!==s)&&(i=!1),H.isNullOrEmpty(s))H.isNullOrEmpty(s)&&(r.length=0);else{var l=s.split(" ");for(let e=r.length-1;e>-1;e--)l.includes(r[e])||r.splice(e,r.length-e)}if(0==r.length)break}if(i){var o=e[0];if((h=G.getElements(o.tagName,o.className))&&h.length>0&&h.length<101){var c=G.getElementClassSiblingIndex(o,o.className),u=new B(o,!0);n.push(u),u.setAttributeValue("class",!0),c>1?(u.setAttributeValue("position()",!0,c-1,"GreaterThan"),G.getNodes(o.parentElement,n,!0)):G.getNodes(o.parentElement,n,!0)}}else if(r.length>0&&!t){o=e[0];var m,h,d=r.join(" "),f=(c=G.getElementClassSiblingIndex(o,d),[]);u=new B(o,!0);f.push(u),u.setAttributeValue("class",!0,op="Contains"),c>1?(u.setAttributeValue("position()",!0,c-1,"GreaterThan"),G.getNodes(o.parentElement,f,!0),m="//"+o.tagName+"[contains(@class,'"+d+"')][position()>"+(c-1)+"]"):(G.getNodes(o.parentElement,f,!0),m="//"+o.tagName+"[contains(@class,'"+d+"')]"),(h=G.selectElements(m))&&h.length>0&&h.length>e.length&&(n=f)}return n}static getElements(e,t){if(H.isNullOrEmpty(e)||H.isNullOrEmpty(t))return null;var n="//"+e+"[@class='"+t+"']";return G.selectElements(n)}static getElementSiblingIndex(e){var t=1;let n=e.previousElementSibling;for(;n;)n.localName==e.localName&&t++,n=n.previousElementSibling;return t}static getElementClassSiblingIndex(e,t){var n=1;let i=e.previousElementSibling;for(;i;)i.localName==e.localName&&(H.isNullOrEmpty(t)||i.className.includes(t))&&n++,i=i.previousElementSibling;return n}static getSelectText(e){let t="",n=e.selectedIndex;if(e.options&&e.options.length>0){const i=e.options.item(n);i&&(t=i.text)}return t}static getSingleLineTextSummary(e,t=60){if(!e)return"";var n="";return(n=e.textContent&&!H.isNullOrEmpty(e.textContent.trim())?G.getInnerText(e).replace(/\s+/g," "):H.isNullOrEmpty(e.innerHTML)?e.tagName:e.innerHTML.replace(/\s+/g," ")).length<=t?n:n.substr(0,t)+"..."}static getInnerText(e){if(!e)return"";let t=e.textContent;return t.replace(/^((\\r\\n)|(\\r)|(\\n)|(\s)|(\\t))/,"").replace(/((\\r\\n)|(\\r)|(\\n)|(\s)|(\\t))$/,""),t}static getHref(e){return e&&(e instanceof HTMLAnchorElement||e instanceof HTMLLinkElement)?e.href:""}static getSrc(e){return e&&e instanceof HTMLImageElement?e.src:""}static checkParent(e,t){if(e&&t){if(t.isEqualNode(e))return!0;for(;t.parentElement;){if(t.parentElement.isEqualNode(e))return!0;t=t.parentElement}}return!1}static getRelativeXpath(e,t){let n=1,i="";if(e&&t&&t.parentElement&&!t.isEqualNode(e)){let r=t.parentElement.children;if(r&&r.length>0){for(let e=0;e<r.length;e++){let i=r[e];if(i.tagName==t.tagName){if(i.isEqualNode(t))break;n++}}i=`${this.getRelativeXpath(e,t.parentElement)}/${t.tagName}[${n}]`}}return i}static getElementTagName(e){let t=e.tagName;if(e instanceof HTMLInputElement){let n=e.getAttribute("type");switch(n=null==n?"":n.toLowerCase(),n){case"file":case"button":case"submit":case"reset":t="BUTTON";break;case"checkbox":t="CHECKBOX";break;case"radio":t="RADIO"}}return t}static matchElementType(e,...t){if(e&&e.nodeType===Node.ELEMENT_NODE){const n=this.getElementTagName(e).toUpperCase();for(const e of t)if(e===n)return!0}return!1}static getFrameOffset(e){const t=parseInt(window.getComputedStyle(e,null).getPropertyValue("padding-left"))||0,n=parseInt(window.getComputedStyle(e,null).getPropertyValue("padding-top"))||0;return{x:t+(parseInt(window.getComputedStyle(e,null).getPropertyValue("border-left-width"))||0),y:n+(parseInt(window.getComputedStyle(e,null).getPropertyValue("border-top-width"))||0)}}static fromDOMRect(e){return{x:Math.round(e.x),y:Math.round(e.y),width:Math.round(e.width),height:Math.round(e.height)}}static getFrameIndex(e){if(null===e)return-1;var t=document.getElementsByTagName("iframe");return t.length>0?[...t].indexOf(e):-1}static getFrameIdByFrameElement=(e,t)=>{const n=e.getAttribute("oct-frame-id");if(n)return t.find((e=>e.id==n));var i=t.filter((t=>t.url==e.src));if(1==i.length)return i[0];if(i.length>0){const t=document.querySelectorAll("iframe"),n=Array.from(t).filter((t=>{const n=t.getAttribute("src");return n&&n==e.src})).indexOf(e);if(-1!=n&&n<i.length)return i[n]}return t[G.getFrameIndex(e)]};static getScrollableAncestor=(e,t)=>{for(var n=e;n&&n.scrollHeight<=n.clientHeight;)n=n.parentElement;return n||(n=e),n};static initialLower=e=>e.length>=1?e.replace(e[0],e[0].toLowerCase()):e;static trimHtml=(e,t=500)=>{if(e&&e.length>t){const n=e.match(/<\/[^>]+>\s*$/);if(n&&n.length>0){const i=n[0];let r=t-i.length;r=r<1?1:r;const a=e.substr(0,r);e=`${a}...${i}`}else e=e.substring(0,parseInt(t))+"..."}return e};static formateDateTime=(e,t)=>{let n={"M+":e.getMonth()+1,"d+":e.getDate(),"h+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds(),"q+":Math.floor((e.getMonth()+3)/3),S:e.getMilliseconds()};for(var i in/(y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length))),n)new RegExp("("+i+")").test(t)&&(t=t.replace(RegExp.$1,1==RegExp.$1.length?n[i]:("00"+n[i]).substr((""+n[i]).length)));return t};static getElementFromPoint=(e,t)=>{let n=document.elementFromPoint(e,t);for(var i=null;n&&n.shadowRoot&&i!=n.shadowRoot;)i=n.shadowRoot,n=n.shadowRoot.elementFromPoint(e,t);return n};static getElementText=e=>{let t="";if(e)if(e instanceof HTMLInputElement)t=e.value??"";else{const n=e.innerText??"",i=e.textContent??"";t=n.length>=i.length?n:i}return t};static getClassName=e=>{let t=(e instanceof SVGElement?e.getAttribute("class")??"":e.className??"").split(" ");return t=t.filter((e=>!k.includes(e))),t.join(" ")}}class ${static getSelectedElementInfo=function(e,t,n=null){let i=window.document.title,r=window.location.href;const a=G.getElementTagName(e),s=e.getAttributeNames().reduce(((t,n)=>({...t,[n]:D.getElementAttributeStringValue(e,n)??""})),{});let l=e.innerText??"";l=l.replace(/[\r\n]/g,""),l.length>35&&(l=l.slice(0,35)+"...");let o=t,c=[];try{o=G.getXPath(e),c=G.getNodes(e)}catch(e){}return null!=n&&(t=n.xpath+t,o=n.xpath+o,c.push(...n.nodes)),{pageTitle:i,pageUrl:r,tag:a,xpath:t,selectedXpath:o,nodes:c,text:l,iframeXPath:null,attributes:s,isSimilarList:!1}};static getSelectedSimilarElementInfo=function(e,t,n,i=null){let r=window.document.title,a=window.location.href;const s=G.getElementTagName(e),l=e.getAttributeNames().reduce(((t,n)=>({...t,[n]:D.getElementAttributeStringValue(e,n)??""})),{});let o=e.innerText??"";o=o.replace(/[\r\n]/g,""),o.length>35&&(o=o.slice(0,35)+"...");let c=t;try{c=G.getXPath(e)}catch(e){}return null!=i&&(t=i.xpath+t,c=i.xpath+c,n.push(...i.nodes)),{pageTitle:r,pageUrl:a,tag:s,xpath:t,selectedXpath:c,nodes:n,text:o,iframeXPath:null,attributes:l,isSimilarList:!0}};static setOctopusRpaStyleTag=function(e=null){let t=null!=e?e.doc:document;if(null!=t.getElementById("octopus-rpa"))return;const n="\n            .octopus-rpa-similar-preselected {\n                background-image: radial-gradient(ellipse, rgba(240,92,117, 0.5), rgba(240,92,117, 0.5)) !important;\n                outline: 3px #FF8888 dashed !important;\n            }\n            .octopus-rpa-similar-selected {\n                background-image: radial-gradient(ellipse, rgba(103,171,73, 0.5), rgba(103,171,73, 0.5)) !important;\n                outline: 3px #61C584 solid !important;\n            }";let i=document.createElement("style");if(t instanceof DocumentFragment)t.appendChild(i);else{(t.head||t.getElementsByTagName("head")[0]).appendChild(i)}i.type="text/css",i.id="octopus-rpa",i.styleSheet?i.styleSheet.cssText=n:i.appendChild(document.createTextNode(n))};static clearSimilarElementStyle=function(){null!=U&&U.classList.remove("octopus-rpa-similar-selected");for(var e=0;e<j.length;e++)j[e].element.classList.remove("octopus-rpa-similar-preselected")};static flickerSimilarElementsByXPath=function(e,t=null){const n="octopus-rpa-similar-selected";let i=null!=t?t.doc:document,r=G.selectElements(e,i);for(var a=1;a<=5;a++)window.setTimeout((()=>{for(var e=0;e<r.length;e++)r[e].classList.add(n)}),200*a-100),window.setTimeout((()=>{for(var e=0;e<r.length;e++)r[e].classList.remove(n)}),200*a)};static selectFirstSimilarElement=function(e,t=null){$.clearSimilarElementStyle(),$.setOctopusRpaStyleTag(t);U=e,z=null,U.classList.add("octopus-rpa-similar-selected");let n=document;null!=t&&(n=t.doc);var i=re.findSimilarElements(U,!0,n);if(null===i||i.elements.length<=0)return null;K=i.rowXPath;for(var r=0;r<i.elements.length;r++)j.push(i.elements[r]),i.elements[r].element!==U&&i.elements[r].element.classList.add("octopus-rpa-similar-preselected");return null};static selectSecondSimilarElement=function(e,t=null){if(null==U||e==U)return null;$.setOctopusRpaStyleTag(t),$.clearSimilarElementStyle(),z=e;let n=null,i=j.map((e=>e.element));if(i.length>=2&&i.indexOf(U)>=0&&i.indexOf(z)>=0){const e=j[0].relativeXPath,t=e.indexOf("/");n=t>=0?K+e.substring(t):K}else n=G.getDynamicXPath(new Array(U,z),!0);let r=G.getDynamicNodes(new Array(U,z),!0);return z=null,U=null,K="",j=[],n?($.flickerSimilarElementsByXPath(n,t),$.getSelectedSimilarElementInfo(e,n,r,t)):void 0};static clearSimilarSelect=function(){$.clearSimilarElementStyle(),U=null,z=null,j=new Array,K=""}}let U=null,z=null,j=new Array,K="";class Z{static get(e){var t=Z.String[navigator.language];void 0===t&&(t=Z.String.en);var n=e.split(".");if(n.length<=1)return null==t[e]?"":t[e].toString();for(var i=t[n[0]],r=1;r<n.length;r++)if(null==(i=i[n[r]]))return"";return i.toString()}}Z.String={"zh-CN":{defualtFieldNames:{title:"标题",titleLink:"标题链接",image:"图片",field:"字段",link:"链接",cardNumber:"编号",price:"价格",date:"时间",source:"来源",author:"作者",tag:"标签",keywords:"关键词",pubTime:"发布时间",text:"正文",abstract:"摘要",overview:"概要",upvote:"顶",downvote:"踩",comment:"评论",content:"内容"}},en:{defualtFieldNames:{title:"title",titleLink:"titleLink",image:"image",field:"field",link:"link",cardNumber:"cardNumber",price:"price",date:"date",source:"source",author:"author",tag:"tag",keywords:"keywords",pubTime:"pubTime",text:"text",abstract:"abstract",overview:"overview",upvote:"upvote",downvote:"downvote",comment:"comment",content:"content"}}};class Y{constructor(){this.xPath="",this.rowXPath="",this.elements=[],this.removedIndexs=[],this.originalCount=0}get hasSimiliar(){return this.elements.length>0}get isValid(){return!!this.rowXPath&&!!this.xPath&&this.elements.length>1}}class J{constructor(){this.element=new ee,this.childrenXPathArray=[]}}class Q{constructor(){this.similar=new Y,this.subtrees=[],this.fullestSubtreeElements=[],this.fullestChildren=[]}}class ee{constructor(){this.absoluteXPath="",this.relativeXPath="",this.backupRelativeXPath="",this.isNoise=!1}getPosition(){if(!this.element||H.isNullOrEmpty(this.relativeXPath)||this.relativeXPath.length<3)return 0;if(this.relativeXPath.startsWith("[")){const e=this.relativeXPath.indexOf("]");if(e-1>0){const t=this.relativeXPath.substr(1,e-1),n=Number(t);return n>0?n:0}}return 0}}const te={SIMILAR_RATIO_THRESHOLD:.65,SIMILAR_GROUP_RATIO_THRESHOLD:.7},ne=["/table","/ul","/ol"];let ie=new Array;class re{static reindexXPath(e,t,n){let i,r,a=!0,s=0;const l=new Array,o=/^\d+$/;for(let c=e.length-1;c>=0;c-=1){const u=o.exec(e[c]);if(u&&u.length>0){let l=parseInt(u[0],10);s===t&&(i=l,n?l+=1:l-=1,r=l.toString(),c-1>=0&&ne.includes(e[c-1])&&(a=!1)),s+=1}c%2==1?(l.push("]"),r&&r.length>0?(l.push(r),r=null):l.push(e[c]),l.push("[")):l.push(e[c])}l.reverse();return{outIsContinue:a,outCurrentIndex:i,outNewXPath:l.join("")}}static rebuildXPath(e,t){if(e.length!==t.length)return null;let n="",i="";for(let r=0;r<e.length;r+=1)e[r]===t[r]?(r%2==1&&(n+="["),n+=e[r],r%2==1&&(n+="]")):i=n;return{outCommonXPath:n,outRowXPath:i}}static compareTwoSimilarElementsDom(e,t){if(e.nodeType!==t.nodeType)return!1;if(e.nodeType!==Node.ELEMENT_NODE)return!0;if(e.localName!==t.localName)return!1;if("p"===e.localName.toLowerCase())return!0;let n=new Array;e.hasChildNodes()&&(n=Array.from(e.children));let i=new Array;if(t.hasChildNodes()&&(i=Array.from(t.children)),!n&&!i||0===n.length&&0===i.length)return!0;if(n.length!==i.length)return!1;const r=n.length;for(let e=0;e<r;e+=1){const t=this.compareTwoSimilarElementsDom(n[e],i[e]);if(!t)return t}return!0}static computeSiblingElements(e,t){if(!e||!t)return null;const n=H.split(G.getIndexedXPath(e),/\[|\]/,_.None),i=H.split(G.getIndexedXPath(t),/\[|\]/,_.None),r=this.rebuildXPath(n,i),a=r.outCommonXPath;let s=r.outRowXPath;if(!H.isNullOrEmpty(a)){H.isNullOrEmpty(s)&&(s=a);const e=this.hasTableTitleRow(document,s),t=e.outRealRowXpath,n=e.outHasTitle,i=new Q,r=new Y;let l=new Array;if(n){r.xPath=t+a.substr(s.length),r.rowXPath=t,l=G.selectElements(t);const e=this.checkRowElementsIsPositioning(r.rowXPath);if(!l||0===l.length)return null;r.elements=new Array,l.forEach((t=>{const n=new ee;n.element=t,n.absoluteXPath=G.getIndexedXPath(t),e&&e.length>0&&(n.relativeXPath=this.getRepositionRelativeXpath(e.index,n.absoluteXPath)),r.elements.push(n)}))}else{if(r.xPath=a,r.rowXPath=s,l=G.selectElements(s),!l||0===l.length)return null;r.elements=new Array,l.forEach((e=>{const t=new ee;t.element=e,t.absoluteXPath=G.getIndexedXPath(e),t.relativeXPath=t.absoluteXPath.substr(r.rowXPath.length,t.absoluteXPath.length-r.rowXPath.length),r.elements.push(t)}))}return i.fullestChildren=this.getFullestChildren(r.elements,r.rowXPath),i.similar=r,i}return null}static computeCommonParent(e,t){const n=G.getIndexedXPath(t.parentElement);let i=null,r=null,a=!1;if(H.isNullOrEmpty(n)||"//body"===n.toLowerCase())return{hasCommonParent:!1};for(let s=0;s<e.length;s+=1){const l=e[s],o=G.getIndexedXPath(l.parentElement);if(H.isNullOrEmpty(o)||"//body"===o.toLowerCase())return{hasCommonParent:!1};if(n===o)return i=l.parentElement,r=e.find((e=>!e.isEqualNode(l))).parentElement,a=!0,{hasCommonParent:a,parentElement:i,nextParentElement:r};if(n.length>o.length){if(n.substr(0,o.length)===o){i=l.parentElement;const t=e.find((e=>!e.isEqualNode(l)));return r=t?t.parentElement:null,a=!0,{hasCommonParent:a,parentElement:i,nextParentElement:r}}}else if(n.length<o.length&&o.substr(0,n.length)===n){i=t.parentElement;let a=H.split(o.substr(n.length,o.length-n.length),"/",_.RemoveEmptyEntries).length;const s=e.find((e=>!e.isEqualNode(l)));for(r=s?s.parentElement:null;a>0&&r;)r=r.parentElement,a-=1;return{hasCommonParent:!0,parentElement:i,nextParentElement:r}}}const s=e.map((e=>e.parentElement)).filter((e=>null!==e));return s.length===e.length?this.computeCommonParent(s,t):{hasCommonParent:!1}}static checkTableFirstRow(e){if(!e||e.length<3)return!1;const t=e[0],n=e[1],i=e[2],r=this.compareTwoSimilarElementsDom(t,n),a=this.compareTwoSimilarElementsDom(n,i);return!r&&!(!r&&!a)}static hasTableTitleRow(e,t){const n=t.lastIndexOf("/");if(n<0)return{outHasTitle:!1,outRealRowXpath:t};const i=t.substr(n+1);if(!i||0===i.length)return{outHasTitle:!1,outRealRowXpath:t};let r=!1,a=t;if("tr"===i.toLowerCase()){const n=G.selectElements(t,e);this.checkTableFirstRow(n)&&(a=t+"[position()>1]",r=!0)}return{outHasTitle:r,outRealRowXpath:a}}static checkRowElementsIsPositioning(e){return/\[position\(\)(?:\>|\<)(\d+)\]$/i.exec(e)}static getRepositionRelativeXpath(e,t){let n=t.substr(e);const i=/^\[(?<index>\d+)\]/gi.exec(n);if(i&&i.length>0){const e=i.groups?i.groups.index:null;let t=Number(e);if(t)return t-=1,n="["+t+"]"+n.substr(i.index+i[0].length),n}return""}static getSimilarElements(e,t,n,i,r,a){let s=1;if(t.length>0){const l=H.split(n,"/",_.RemoveEmptyEntries).length-1;let o=t[0];for(let e=0;e<l&&o;e+=1)o=o.parentElement;if(o){let e=o.previousElementSibling;for(;e;)e.localName===o.localName&&(s+=1),e=e.previousElementSibling}for(let l=0;l<t.length;l+=1){const o=t[l],c=new ee;c.element=o,c.absoluteXPath=a?G.getIndexedXPath(o):e+G.stringFormat(n,s),r&&r.length>0?c.relativeXPath=this.getRepositionRelativeXpath(r.index,c.absoluteXPath):c.relativeXPath=c.absoluteXPath.substr(i.rowXPath.length,c.absoluteXPath.length-i.rowXPath.length),i.elements.push(c),s+=1}}return i}static getChildrenXPath(e,t,n,i,r){if(!e)return r;if(!G.isHTMLElement(e))return r;if("body"===e.localName.toLowerCase())return r;let a=e.firstElementChild;if(!a)return r;const s={};for(;a;){const e=a.localName;if(H.isNullOrEmpty(e)){a=a.nextElementSibling;continue}let l=1;e in s?(l=s[e],l+=1,s[e]=l):s[e]=l;let o=!0;i&&H.isNullOrEmpty(G.getElementText(a))&&(o=!1);const c=(n||"")+"/"+e+"["+l+"]";o&&r.push(c),r=this.getChildrenXPath(a,t,c,i,r),a=a.nextElementSibling}return r}static initSimilarityDegreeMapping(e){ie=new Array;for(let t=0;t<e;t+=1)ie[t]=new Array(e)}static getLowSimilarityDegreeIndexes(){const e=new Array;for(let t=0;t<ie.length;t+=1)this.computeSimilarRatioInGroup(t)<.5&&e.push(t);return e}static computeSimilarRatioInGroup(e){const t=ie[e];let n=0;for(let i=0;i<t.length;i+=1)i!==e&&t[i]>te.SIMILAR_RATIO_THRESHOLD&&(n+=1);return n/(t.length-1)}static computeSimilarRatio(e,t){const n=e.childrenXPathArray.filter((e=>t.childrenXPathArray.includes(e)));return 2*(null!==n?n.length+1:1)*1/(e.childrenXPathArray.length+1+(t.childrenXPathArray.length+1))}static overlapChildNodes(e){if(!e||e.length<=0)return[];this.initSimilarityDegreeMapping(e.length);const t=new Array;for(let n=0;n<e.length;n+=1){const i=e[n];for(let r=n+1;r<e.length;r+=1){const a=e[r],s=i.childrenXPathArray.filter((e=>a.childrenXPathArray.includes(e))),l=2*(null!==s?s.length+1:1)*1/(i.childrenXPathArray.length+1+(a.childrenXPathArray.length+1));ie[n][r]=ie[r][n]=l,t.push(l)}}return t}static isSimilarValid(e,t){if(!e||e.length<=0)return!1;const n=this.overlapChildNodes(e),i=n.filter((e=>e>te.SIMILAR_RATIO_THRESHOLD));return 1*(i?i.length:0)/n.length>=t}static getSimilarElementTree(e){if(!e||e.length<=0)return null;const t=new Array;for(let n=0;n<e.length;n+=1){const i=e[n];if(!i.element||!i.absoluteXPath.length)continue;let r=new Array;r=this.getChildrenXPath(i.element,i.absoluteXPath,null,!1,r);const a=new J;a.element=i,a.childrenXPathArray=r,t.push(a)}const n=new Q;return n.subtrees=t,n}static getFirstSimilarSubtree(e,t){for(let n=0;n<e.subtrees.length;n+=1)if(!t.includes(n))return e.subtrees[n];return null}static filterElementsFamily(e,t,n){const i=this.getLowSimilarityDegreeIndexes();for(let r=i.length-1;r>-1;r-=1){if(t.subtrees[i[r]].element.absoluteXPath===n)return null;e.elements[i[r]].isNoise=!0}const r=t.subtrees.length;if(e.elements.length>r){const a=this.getFirstSimilarSubtree(t,i);for(let t=r;a&&t<e.elements.length;t+=1){const r=e.elements[t];let s=new Array;s=this.getChildrenXPath(r.element,r.absoluteXPath,null,!1,s);const l=new J;if(l.element=r,l.childrenXPathArray=s,this.computeSimilarRatio(l,a)<te.SIMILAR_RATIO_THRESHOLD){if(l.element.absoluteXPath===n)return null;e.elements[t].isNoise=!0,i.push(t)}}}let a=new Array;for(let t=1;t<e.elements.length;t+=1){if(e.elements[t].isNoise)continue;const n=e.elements[t-1].element.className,{className:i}=e.elements[t].element;if(1===t&&(a=n.split(" ")),H.isNullOrEmpty(i))H.isNullOrEmpty(i)&&(a.length=0);else{const e=i.split(" ");for(let t=a.length-1;t>-1;t-=1)e.includes(a[t])||a.splice(t,a.length-t)}if(0===a.length)break}const s=a.join(" ");for(let t=e.elements.length-1;t>-1;t-=1)if(e.elements[t].isNoise){const{className:n}=e.elements[t].element;!H.isNullOrEmpty(n)&&n.includes(s)&&(a.length=0),e.elements.splice(t,1)}return a.length>0?e.originalCount=e.elements.length:e.removedIndexs=i,e}static reviewSimilarElements(e,t){const n=new Q;if(!e||e.rowXPath.length<0||!e.elements||e.elements.length<=0)return n;let i=e.elements;e.originalCount=e.elements.length,e.elements.length>20&&(i=e.elements.slice(0,20));const r=this.getSimilarElementTree(i);let a=!1;if(r)if(r.subtrees.length>10){if(a=this.isSimilarValid(r.subtrees,te.SIMILAR_GROUP_RATIO_THRESHOLD),a){if([...r.subtrees].sort(((e,t)=>{const n=e.childrenXPathArray.length-t.childrenXPathArray.length;return n<0?1:n>0?-1:0}))[0].childrenXPathArray.length>5){const n=this.filterElementsFamily(e,r,t);n?e=n:a=!1}}}else a=this.isSimilarValid(r.subtrees,.6);return a&&(n.similar=e),n}static getElementText(e){if(!e)return"";let t="",n=e.firstChild;for(;n;)3===n.nodeType&&(t+=n.textContent.trim()),n=n.nextSibling;return t}static findSimilarElements(e,t=!0,n=document){if(!e||e.nodeType!==Node.ELEMENT_NODE)return null;const i=G.getIndexedXPath(e),r=i.split(/\[|\]/),a=r.length/2;let s,l=new Array;for(let o=0;o<a;o+=1){let a=0,c="",u=this.reindexXPath(r,o,!0);if(!1===u.outIsContinue||i===u.outNewXPath)break;a=u.outCurrentIndex,c=u.outNewXPath;let m=G.selectElements(c,n);if((null===m||0===m.length)&&a>1){if(u=this.reindexXPath(r,o,!1),!1===u.outIsContinue||i===u.outNewXPath)break;c=u.outNewXPath,m=G.selectElements(c,n)}if(m&&1===m.length){const i=c.split(/\[|\]/);let a="",o="",u=!1;const h=this.rebuildXPath(r,i);c=h.outCommonXPath,a=h.outRowXPath;const d=this.hasTableTitleRow(e,a);u=d.outHasTitle,o=d.outRealRowXpath,c=o+c.substr(a.length),m=G.selectElements(c,n);let f=!1;for(let e=m.length-1;e>-1;e-=1)G.isInvalidElement(m[e])&&(m.splice(e,1),f=!0);if(m&&m.length>1&&m.length>l.length){l=m,s=new Y,s.elements=new Array;const e=m[0],n=a.substr(0,a.lastIndexOf("/")),i=G.getIndexedXPath(e).substr(n.length),r=i.indexOf("]"),h=i.substr(0,i.indexOf("[")+1)+"{0}"+i.substr(r,i.length-r);if(u){const e=c.substr(o.length);s.xPath=o+e,s.rowXPath=o;const t=this.checkRowElementsIsPositioning(s.rowXPath);s=this.getSimilarElements(n,m,h,s,t,f)}else s.xPath=c,s.rowXPath=a,s=this.getSimilarElements(n,m,h,s,null,f);if(!t)break}}}if(s){return this.reviewSimilarElements(s,i).similar}return null}static findSimilarElementsFullestChilds(e,t){const n=this.getFullestChildren(e,t);return null===n?[]:n}static getFullestChildren(e,t){if(!e||e.length<=0)return[];const n=new Map;if(e.forEach((e=>{e.element&&!H.isNullOrEmpty(e.absoluteXPath)&&this.getChildrenSimilarElement(e.element,e.absoluteXPath,"","",!0,n)})),!n||0===n.size)return[];const i=Array.from(n.values());return this.calculateRelativeXPathToRows(e,t,i),i}static getChildrenSimilarElement(e,t,n,i,r,a){if(!e)return;if(e instanceof HTMLBodyElement)return;if(e instanceof SVGElement)return;let s=e.firstElementChild;if(!s)return;n=null===n?"":n,i=null===i?"":i;const l=new Map,o=new Map;for(;s;){const e=s.localName;if(H.isNullOrEmpty(e)){s=s.nextElementSibling;continue}let c=1;l.has(e)?(c=l.get(e),c+=1,l.set(e,c)):l.set(e,c);let u=1;const{className:m}=s;let h=!1;if(!H.isNullOrEmpty(m)&&!H.hasInt(m)){h=!0;const t=e+"|"+m;o.has(t)?(u=o.get(t),u+=1,o.set(t,u)):o.set(t,u)}let d=!0;const f=this.getElementText(s);r&&H.isNullOrEmpty(f)&&(d=!1);const g=n+"/"+e+"["+c+"]";let p=null;if(p=h?i+"/"+e+"[@class='"+m+"']["+u+"]":i+"/"+e+"["+c+"]",d&&!a.has(g)&&0!==s.getClientRects().length){const e=G.getShortClassXPath(p),n=new ee;n.element=s,n.absoluteXPath=t+g,n.relativeXPath=g,n.backupRelativeXPath=e,a.set(g,n)}this.getChildrenSimilarElement(s,t,g,p,r,a),s=s.nextElementSibling}}static calculateRelativeXPathToRows(e,t,n){if(H.isNullOrEmpty(t)||!e||0===e.length||!n||0===n.length)return;const i=H.split(t,/\[|\]/,_.RemoveEmptyEntries);if(!i||0===i.length)return;const r=e[0].absoluteXPath;if(H.isNullOrEmpty(r))return;const a=H.split(r,/\[|\]/,_.RemoveEmptyEntries);if(!a||0===a.length)return;let s="";const l=/^\d+$/;for(let e=i.length;e<a.length;e+=1)if(e===i.length){const t=l.exec(a[e]);if(t&&t.length>0)continue;s+=a[e]}else{const t=l.exec(a[e]);t&&t.length>0?s+="["+a[e]+"]":s+=a[e]}n.forEach((e=>{e.relativeXPath=s+e.relativeXPath}))}static computeSiblingElements(e,t){if(!e||!t)return null;const n=H.split(G.getIndexedXPath(e),/\[|\]/,_.None),i=H.split(G.getIndexedXPath(t),/\[|\]/,_.None),r=this.rebuildXPath(n,i),a=r.outCommonXPath;let s=r.outRowXPath;if(!H.isNullOrEmpty(a)){H.isNullOrEmpty(s)&&(s=a);const e=this.hasTableTitleRow(document,s),t=e.outRealRowXpath,n=e.outHasTitle,i=new Q,r=new Y;let l=new Array;if(n){r.xPath=t+a.substr(s.length),r.rowXPath=t,l=G.selectElements(t);const e=this.checkRowElementsIsPositioning(r.rowXPath);if(!l||0===l.length)return null;r.elements=new Array,l.forEach((t=>{const n=new ee;n.element=t,n.absoluteXPath=G.getIndexedXPath(t),e&&e.length>0&&(n.relativeXPath=this.getRepositionRelativeXpath(e.index,n.absoluteXPath)),r.elements.push(n)}))}else{if(r.xPath=a,r.rowXPath=s,l=G.selectElements(s),!l||0===l.length)return null;r.elements=new Array,l.forEach((e=>{const t=new ee;t.element=e,t.absoluteXPath=G.getIndexedXPath(e),t.relativeXPath=t.absoluteXPath.substr(r.rowXPath.length,t.absoluteXPath.length-r.rowXPath.length),r.elements.push(t)}))}return i.fullestChildren=this.getFullestChildren(r.elements,r.rowXPath),i.similar=r,i}return null}static findSiblingElements(e,t,n=!1){if(!t||!t.parentElement||!e||e.length<2)return null;if(e.includes(t))return this.computeSiblingElements(e[0],e[1]);let i=!1,r=null,a=null;if(e.every((n=>n.parentElement!==t.parentElement||(r=n.parentElement,a=e.find((e=>e!==n)).parentElement,i=!0,!1))),!i&&!n){const n=this.computeCommonParent(e,t);i=n.hasCommonParent,r=n.parentElement||null,a=n.nextParentElement||null}return i?this.computeSiblingElements(r,a):null}}const ae={smart:"smart",percision:"percision"},se={text:"text",innerHtml:"innerHtml",outerHtml:"outerHtml",href:"href",src:"src"},le={fixed:"fixed",time:"time",pageTitle:"title",pageUrl:"url",pageSource:"source"};class oe{constructor(){this.item=null,this.name=null,this.ignoreSimilarLoop=!1,this.new=!0,this.data=[],this.score=0,this.extractProperty=se.text}}class ce{constructor(){this.relativeXPath="",this.absoluteXPath="",this.elements=[],this.localName="",this.className="",this.isTags=!1,this.isRichText=!1,this.pattern=!1,this.classXPath=null,this.isRetain=!1}containElement(e){return!!this.elements.includes(e)}}class ue{constructor(){this.subtrees=[],this.row=null,this.firstItem=null}}let me=null;const he=function(e){var t=document,n=null;if(e.indexOf("/shadow-root")>-1){var i=gt.getShadowRootElement(e);null!=i&&(n=i.shadowRoot,t=i.shadowRoot.doc,e=i.xpath)}return null==me&&(we.doc=t,we.shadowRoot=n,me=new de(t,n)),{xpath:e,doc:t}};class de{constructor(e,t=null){this.doc=e,this.shadowRoot=t,this.lastSelectedXpath=null,this.firstSelectedXpath=null,this.lastSelectedDoc=null,this.stateMap={emptyState:this.emptyState,listSelectedState:this.listSelectedState,appendFieldState:this.appendFieldState,preselectedChildElementsState:this.preselectedChildElementsState,preselectedSimilarState:this.preselectedSimilarState},this.reset(ae.smart)}reset(e="smart"){return this.clearSelectedElements(),this.clearPreselectedElements(),we.clearHighlightStyle(),this.context={fields:[],similarLoopXpath:"",similarLoopNodes:[],mode:e},this.selectedElements=[],this.preselectedElements=[],this.smartTree=null,this.state=this.emptyState,this.firstSelectedXpath=null,"percision"==e?this.handleElement(this.lastSelectedXpath,this.lastSelectedDoc):{fields:[],data:[],similarLoopXpath:"",similarLoopNodes:[],replace:!0,state:null}}load(e,t=null){if(this.context.fields=e,null==this.smartTree)this.context.fields.forEach((e=>{null!=e.item&&this.addSelectedElements(e.item.elements)}));else if(null==this.smartTree.firstItem||null==t){let e=this.getAllFieldElements(this.context.fields);this.addSelectedElements(e)}else{var n=Ue(t.selectedXpath,null,this.doc)[0];if(t.name==this.preselectedChildElementsState.name)if(H.isNullOrEmpty(this.context.similarLoopXpath)){let e=Pe(n,t.selectedXpath);this.addPreselectedElements(e),this.addSelectedElements([n])}else{var i=Ie(n,this.smartTree.firstItem.absoluteXPath,this.smartTree.row.absoluteXPath,this.doc);this.addSelectedElements(this.smartTree.firstItem.elements),i.forEach((e=>{this.addPreselectedElements(e.elements)}))}else if(t.name==this.preselectedSimilarState.name)this.addPreselectedElements(this.smartTree.firstItem.elements),this.addSelectedElements([this.smartTree.firstItem.elements[0]]);else{let e=this.getAllFieldElements(this.context.fields);this.addSelectedElements(e)}}return null==t?e.filter((e=>!e.ignoreSimilarLoop)).length>0?this.state=this.listSelectedState:this.state=this.appendFieldState:this.state=this.stateMap[t.name],this.context.fields.map((e=>({name:e.name,data:e.data.map((e=>H.base64Encode(e)))})))}handleElement(e,t=document){var n=G.selectSingleElement(e,t);if(null==n||this.selectedElements.includes(n))return{fields:[],data:[],similarLoopXpath:"",similarLoopNodes:[],replace:!1,state:null};H.isNullOrEmpty(this.firstSelectedXpath)&&(this.firstSelectedXpath=e),this.lastSelectedXpath=e,this.lastSelectedDoc=t,this.context.fields.forEach((e=>e.new=!1)),this.replace=0==this.context.fields.length,t!=this.doc?this.state=this.appendSingleField(n):this.state=this.state(n);let i=this.context.fields.filter((e=>1==e.new)),r=i.map((e=>e.data.map((e=>H.base64Encode(e)))));return{fields:i.map((e=>Le(e,this.context.similarLoopNodes))),data:r,similarLoopXpath:this.context.similarLoopXpath,similarLoopNodes:this.context.similarLoopNodes,replace:this.replace,state:JSON.stringify({name:this.state.name,selectedXpath:this.firstSelectedXpath})}}emptyState(e){if(this.smartTree=ve(e,this.doc),null==this.smartTree)return this.appendFieldState(e);if(this.context.mode==ae.smart)return this.appendSimilarField(this.smartTree.subtrees),this.listSelectedState;{let t=Pe(e,this.smartTree.firstItem.absoluteXPath);return t.length<3?(this.addPreselectedElements(this.smartTree.firstItem.elements),this.appendSingleField(e),this.preselectedSimilarState):(this.addPreselectedElements(t),this.appendSingleField(e),this.preselectedChildElementsState)}}preselectedSimilarState(e){if(this.preselectedElements.includes(e)){if(this.clearPreselectedElements(),this.clearFields(),this.smartTree.firstItem.elements.includes(e))this.appendSimilarField([this.smartTree.firstItem]);else{var t=Ie(this.smartTree.firstItem.elements[0],this.smartTree.firstItem.absoluteXPath,this.smartTree.row.absoluteXPath,this.doc);this.appendSimilarField(t)}return this.listSelectedState}return this.clearPreselectedElements(),this.appendSingleField(e),this.appendFieldState}preselectedChildElementsState(e){var t=Ie(this.selectedElements[0],this.smartTree.firstItem.absoluteXPath,this.smartTree.row.absoluteXPath,this.doc);if(this.preselectedElements.includes(e)){var n=Array.from(this.preselectedElements);return this.clearPreselectedElements(),1==this.selectedElements.length?(this.clearSelectedElements(),t.forEach((e=>{this.addPreselectedElements(e.elements)})),n.forEach((e=>this.appendSingleField(e))),this.preselectedSimilarState):(this.clearFields(),this.appendSimilarField(t),this.listSelectedState)}return this.smartTree.firstItem.elements.includes(e)?(this.clearFields(),this.clearPreselectedElements(),this.appendSimilarField([this.smartTree.firstItem]),this.context.similarLoopXpath=this.smartTree.row.absoluteXPath,t.forEach((e=>{this.addPreselectedElements(e.elements)})),this.preselectedChildElementsState):(this.clearSelectedElements(),this.clearPreselectedElements(),this.appendSingleField(e),this.appendFieldState)}appendFieldState(e){return this.appendSingleField(e),this.appendFieldState}listSelectedState(e){for(var t=0;t<this.context.fields.length;t++)if(null!=this.context.fields[t].item&&this.context.fields[t].item.containElement(e))return this.addSelectedElements(this.context.fields[t].item.elements),this.listSelectedState;for(t=0;t<this.smartTree.subtrees.length;t++)if(this.smartTree.subtrees[t].containElement(e))return this.smartTree.subtrees[t].isRetain=!0,this.appendSimilarField([this.smartTree.subtrees[t]]),this.listSelectedState;for(t=0;t<this.smartTree.row.elements.length;t++)if(this.smartTree.row.elements[t].contains(e)){var n=G.getRelativeXpath(this.smartTree.row.elements[t],e),i=Oe(this.smartTree.row.absoluteXPath+n.toLowerCase(),this.smartTree.row.absoluteXPath,null,this.doc);return i.isRetain=!0,this.appendSimilarField([i]),this.listSelectedState}return this.createSiblingSmartTree(e)||this.appendSingleField(e),this.listSelectedState}createSiblingSmartTree(e){if(this.context.fields.filter((e=>e.ignoreSimilarLoop)).length>0)return!1;var t=re.findSimilarElements(e,!0,this.doc);if(null==t)return!1;let n=[],i=Ue(t.rowXPath,null,this.doc);n.push(...this.smartTree.row.elements),n.push(...i);let r=G.getDynamicXPath(n,!0);if(!H.isNullOrEmpty(r)){let e=Te(r,!0,this.doc);if(null!=e&&null!=this.smartTree){if(e.subtrees.filter((e=>this.smartTree.subtrees.findIndex((t=>t.relativeXPath==e.relativeXPath))>-1))<this.smartTree.subtrees.length)return!1;e.firstItem=this.smartTree.firstItem;var a=this.firstSelectedXpath;return this.reset(),this.firstSelectedXpath=a,this.replace=!0,this.smartTree=e,this.appendSimilarField(e.subtrees),!0}}return!1}appendSimilarField(e){if((e=e.filter((e=>null!=e.elements&&e.elements.length>0&&!H.isNullOrEmpty(e.relativeXPath)))).length>0){H.isNullOrEmpty(this.context.similarLoopXpath)&&(this.context.similarLoopXpath=this.smartTree.row.absoluteXPath,this.context.similarLoopNodes=G.getDynamicNodes(this.smartTree.row.elements,!0),null!=this.shadowRoot&&(this.context.similarLoopXpath=this.shadowRoot.xpath+this.context.similarLoopXpath,this.context.similarLoopNodes.push(...this.shadowRoot.nodes)),this.context.similarLoopNodes=this.context.similarLoopNodes.reverse());var t=Re(this.smartTree.row,e,0===this.context.fields.length,this.doc);t.forEach((e=>{null!=e.item&&(e.item.absoluteNodes=G.getNodes(e.item.elements[0]).reverse(),e.ignoreSimilarLoop||(e.item.relativeNodes=e.item.absoluteNodes.slice(this.context.similarLoopNodes.length-(null==this.shadowRoot?0:this.shadowRoot.nodes.length),e.item.absoluteNodes.length))),e.name=this.getUniqueName(H.isNullOrEmpty(e.name)?$e.resources.defualtFieldNames.field:e.name),this.addSelectedElements(e.item.elements)})),this.context.fields.push(...t)}}appendSingleField(e){if(null==this.smartTree||H.isNullOrEmpty(this.context.similarLoopXpath))var t=Xe(e,null,this.doc);else t=Xe(e,this.smartTree.row.elements,this.doc);t.name=this.getUniqueName(H.isNullOrEmpty(t.name)?$e.resources.defualtFieldNames.field:t.name),t.item.absoluteNodes=G.getNodes(t.item.elements[0]).reverse(),t.item.relativeNodes=[],null!=this.shadowRoot&&(t.item.absoluteNodes=this.shadowRoot.nodes.reverse().concat(t.item.absoluteNodes),t.item.absoluteXPath=this.shadowRoot.xpath+t.item.absoluteXPath,t.item.relativeXPath=this.shadowRoot.xpath+t.item.relativeXPath),null!=e&&this.addSelectedElements([e]),this.context.fields.push(t)}deleteFields(e){return e.forEach((e=>{var t=this.context.fields.findIndex((t=>t.name===e));if(-1!=t){var n=this.context.fields[t];if(null!=n.item){let e=!1;this.context.fields.forEach((t=>{if(t===n)return;const i=t.item?.elements;if(null==i||i.length!==n.item.elements.length)return;n.item.elements.every(((e,t)=>e.isEqualNode(i[t])))&&(e=!0)})),e||(we.clearSelectedStyle(n.item.elements),n.item.elements.forEach((e=>{if(this.selectedElements.includes(e)){var t=this.selectedElements.indexOf(e);this.selectedElements.splice(t,1)}}))),we.clearHighlightStyle(n.item.elements)}this.context.fields.splice(t,1)}})),0==this.context.fields.length&&this.reset(),!0}addField(e,t){let n=0;if(null!=t){var i=this.context.fields.find((e=>e.name===t));i&&(n=this.context.fields.indexOf(i)+1)}return e.item&&(e.item.absoluteNodes=G.getNodes(e.item.elements[0]).reverse(),e.ignoreSimilarLoop||(e.item.relativeNodes=e.item.absoluteNodes.slice(this.context.similarLoopNodes.length-(null==this.shadowRoot?0:this.shadowRoot.nodes.length),e.item.absoluteNodes.length)),this.addSelectedElements(e.item.elements)),this.context.fields.splice(n,0,e),{field:Le(e),data:e.data.map((e=>H.base64Encode(e)))}}updateField(e,t){var n=this.context.fields.findIndex((e=>e.name===t));if(n>-1){let t=this.context.fields[n],i=null;return n>0&&(i=this.context.fields[n-1].name),this.deleteFields([t.name]),this.addField(e,i).data}return[]}setOrder(e){let t=new Array;return e.forEach((e=>{var n=this.context.fields.find((t=>t.name===e));n&&t.push(n)})),this.context.fields=t,!0}addSelectedElements(e){e.forEach((e=>{-1===this.selectedElements.indexOf(e)&&this.selectedElements.push(e);var t=this.preselectedElements.indexOf(e);-1!=t&&this.preselectedElements.splice(t,1)})),we.addSelectedStyle(e)}addPreselectedElements(e){e.forEach((e=>{-1===this.preselectedElements.indexOf(e)&&this.preselectedElements.push(e);var t=this.selectedElements.indexOf(e);-1!=t&&this.selectedElements.splice(t,1)})),we.addPreselectedStyle(e)}clearPreselectedElements(e=!1){this.preselectedElements&&(we.clearPreselectedStyle(this.preselectedElements),e||(this.preselectedElements=[]))}clearSelectedElements(e=!1){this.selectedElements&&(we.clearSelectedStyle(this.selectedElements),e||(this.selectedElements=[]))}clearFields(){this.replace=!0,this.context.fields=[],this.clearSelectedElements()}createUniqueField(e,t,n){var i=new oe;return i.ignoreSimilarLoop=t,i.extractProperty=e,i.name=this.getUniqueName(),i.data=new Array(H.isNullOrEmpty(this.context.similarLoopXpath)||null==this.smartTree||0==this.smartTree.row.elements.length?1:this.smartTree.row.elements.length).fill(We(e,n,!0)),i}createSmartField(e,t,n){var i=new oe;return i.ignoreSimilarLoop=n,i.name=this.getUniqueName(),i.extractProperty=t,i.item=n?Oe(e,null,null,this.doc):Oe(this.context.similarLoopXpath+e,this.context.similarLoopXpath,null,this.doc),i.data=_e(i.ignoreSimilarLoop?i.item.absoluteXPath:i.item.relativeXPath,t,n,H.isNullOrEmpty(this.context.similarLoopXpath)?[]:this.smartTree.row.elements,!0,this.doc),i}getUniqueName(e=$e.resources.defualtFieldNames.field){let t=1,n=e,i=e;for(;;){if(0==this.context.fields.filter((e=>e.name==i)).length)return i;i=n+t.toString(),t+=1}}getAllFieldElements(e){let t=[];return e.forEach((e=>{null!=e.item&&(null!=this.smartTree.row?this.smartTree.row.elements.forEach((n=>{let i=null;i=e.ignoreSimilarLoop?Ue(e.item.absoluteXPath,null,this.doc)[0]:Ue(e.item.relativeXPath,n,this.doc)[0],i&&t.push(i)})):t.push(...e.item.elements))})),t}}const fe="octopus-rpa-stateMachine",ge="octopus-rpa-stateMachine-selected",pe="octopus-rpa-stateMachine-preselected",Ee="octopus-rpa-stateMachine-highlight",xe="octopus-rpa-stateMachine-verify";class we{static setStateMachineStyleTag(){if(null!=this.doc.getElementById(fe))return;const e=`\n            .${pe} {\n                background-image: radial-gradient(ellipse, rgba(255, 60, 33, 0.10), rgba(255, 60, 33, 0.10)) !important;\n                outline: 2px #FF3C21 dashed !important;\n                outline-offset: -2px;\n            }\n            .${ge} {\n                background-image: radial-gradient(ellipse, rgba(0, 162, 59, 0.20), rgba(0, 162, 59, 0.20)) !important;\n                outline: 2px #00A23B solid !important;\n                outline-offset: -2px;\n            }\n            .${Ee} {\n                background-image: radial-gradient(ellipse, rgba(10, 110, 255, 0.20), rgba(10, 110, 255, 0.20)) !important;\n                outline: 2px #0A6EFF solid !important;\n                outline-offset: -2px;\n            }\n            .${xe} {\n                background-image: radial-gradient(ellipse, rgba(245, 63, 63, 0.20), rgba(245, 63, 63, 0.20)) !important;\n                outline: 2px #F53F3F solid !important;\n                outline-offset: -2px;\n            }`;let t=document.createElement("style");if(this.doc instanceof DocumentFragment)this.doc.appendChild(t);else{(this.doc.head||this.doc.getElementsByTagName("head")[0]).appendChild(t)}t.type="text/css",t.id=fe,t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e))}static addHighlightStyle(e){this.setStateMachineStyleTag(),e.forEach((e=>{we.addClass(e,Ee)}))}static addPreselectedStyle(e){this.setStateMachineStyleTag(),e.forEach((e=>{we.removeClass(e,ge),we.addClass(e,pe)}))}static addSelectedStyle(e){this.setStateMachineStyleTag(),e.forEach((e=>{we.removeClass(e,pe),we.addClass(e,ge)}))}static clearPreselectedStyle(e=null){let t=e;null==t&&(t=G.selectElements(`//*[contains(@class,"${pe}")]`,this.doc)),t.forEach((e=>{we.removeClass(e,pe)}))}static clearSelectedStyle(e=null){let t=e;null==t&&(t=G.selectElements(`//*[contains(@class,"${ge}")]`,this.doc)),t.forEach((e=>{we.removeClass(e,ge)}))}static clearHighlightStyle(e=null){let t=e;null==t&&(t=G.selectElements(`//*[contains(@class,"${Ee}")]`,this.doc)),t.forEach((e=>{we.removeClass(e,Ee)}))}static flicker(e=null){if(null!=e&&e.length>0){we.setStateMachineStyleTag();const n=5,i=200;for(var t=0;t<n;t++)window.setTimeout((()=>{for(var t=0;t<e.length;t++)we.addClass(e[t],xe)}),2*i*t-i),window.setTimeout((()=>{for(var t=0;t<e.length;t++)we.removeClass(e[t],xe)}),2*i*t)}}static addClass(e,t){e&&e.classList&&!e.classList.contains(t)&&e.classList.add(t)}static removeClass(e,t){e&&e.classList&&e.classList.contains(t)&&e.classList.remove(t)}}we.doc=document,we.shadowRoot=null;class ye{constructor(){this.name="",this.extractProperty="",this.xpath="",this.ignoreSimilarLoop=!1}}class be{static selected(e,t=document){return me.handleElement(e,t)}static load(e,t,n,i=document){if(H.isNullOrEmpty(t)){let t=!1;for(var r=0;r<e.length;r++){Ue(e[r].elementConfig.xpath,null,i)[0]&&(t=!0)}if(!t)return me=null,{success:!1,errorCode:"fieldXPathNotExists"}}else{if(0==Ue(t,null,i).length)return me=null,{success:!1,errorCode:"similarLoopXpathNotExists"}}me.reset();let a=null,s=null;if(H.isNullOrEmpty(t)||(a=Te(t,!1,i)),!H.isNullOrEmpty(n)&&(s=JSON.parse(n),!H.isNullOrEmpty(s.selectedXpath)&&"listSelectedState"!=s.name&&"appendFieldState"!=s.name)){var l=Ue(s.selectedXpath,null,i)[0];if(l){me.firstSelectedXpath=s.selectedXpath;let e=ve(l,i);null==a&&(a=e),null!=e&&(a.firstItem=e.firstItem)}if(!l||null==a.firstItem)return me=null,{success:!1,errorCode:"fieldXPathNotExists"}}if(!H.isNullOrEmpty(t)&&null==a)return me=null,{success:!1,errorCode:"fieldXPathNotExists"};me.smartTree=a,me.context.similarLoopXpath=t;let o=e.map((e=>this.createSmartFieldByFieldDto(e)));var c=!1;for(r=0;r<e.length;r++)o[r].name=e[r].name,null!=o[r].data&&o[r].data.length>0&&(c=!0);return c?{success:!0,result:me.load(o,s)}:(me=null,{success:!1,errorCode:"fieldXPathNotExists"})}static async highlight(e,t,n=document){we.clearHighlightStyle();let i=[],r=[];H.isNullOrEmpty(t)||(r=G.selectElements(t,n)),e.forEach((({field:e,rows:t,fieldXpath:a,ignoreSimilarLoop:s})=>{a=e?e.elementConfig.xpath:a;if(s=e?e.elementConfig.ignoreSimilarLoop:s){let e=Ue(a,null,n)[0];e&&!i.includes(e)&&i.push(e)}else t.forEach((e=>{let t=r[e];if(t){let e=Ue(a,t,n)[0];e&&i.push(e)}}))}));for(var a=!0,s=0;s<i.length;s++){if(await D.isElementOnViewport(i[s])){a=!1;break}}if(a&&i.length>0){const e=i[0].ownerDocument.documentElement,t=e.style.cssText;e.style.scrollBehavior="auto",i[0].scrollIntoView({behavior:"instant",block:"center",inline:"nearest"}),e.style.cssText=t}return we.addHighlightStyle(i),!0}static reset(e){return null!=me?me.reset(e):null}static delete(e){return null!=me?me.deleteFields(e):null}static add(e,t){if(null!=me){let n=this.createSmartFieldByFieldDto(e);return n.name=e.name,me.addField(n,t)}return null}static update(e,t){if(null!=me){let n=this.createSmartFieldByFieldDto(e);return n.name=e.name,me.updateField(n,t)}return null}static setOrder(e){return null!=me?me.setOrder(e):null}static canLoad(e,t,n=document){let i=!1;if(null!=me)return{canLoad:i,errorCode:"hasLoaded"};if(!H.isNullOrEmpty(t)){if(0==Ue(t,null,n).length)return{canLoad:i,errorCode:"similarLoopXpathNotExists"}}for(var r=0;r<e.length;r++){let a=0,s=e[r];if("element"==G.initialLower(s.type)){let e=[];e=s.elementConfig.ignoreSimilarLoop?Ue(s.elementConfig.xpath,null,n):Ue(t+s.elementConfig.xpath,null,n),a=e.length}if(a>0){i=!0;break}}return i?{canLoad:i}:{canLoad:i,errorCode:"fieldXPathNotExists"}}static verify(e,t,n,i=document){let r=[];return r=Ue(t?e:n+e,null,i),we.flicker(r),r.length}static createSmartFieldByFieldDto(e){let t=G.initialLower(e.type),n=t,i="";return"element"==t?(n=G.initialLower(e.elementConfig.extractProperty),me.createSmartField(e.elementConfig.xpath,n,e.elementConfig.ignoreSimilarLoop)):"page"==t?(n=G.initialLower(e.pageConfig.property),me.createUniqueField(n,!0,i)):(t==le.fixed&&null!=e.fixedConfig&&(i=e.fixedConfig.value),me.createUniqueField(n,!0,i))}}const Ne=function(e,t){for(const n of e)t.push(n.XPath),Ne(n.ChildNodes,t)},ve=function(e,t=document){var n="";if("td"==e.tagName.toLowerCase()||"th"==e.tagName.toLowerCase()){var i=G.getXPath(e),r=i.lastIndexOf("/tr");if(!(r>=0))return null;n=i.substring(0,r+3),l=(l=i.substring(n.length,i.length)).substring(l.indexOf("/"),l.length);var a=G.selectElements(n+l,t)}else{var s=re.findSimilarElements(e,!0,t);if(null==s||0==s.elements.length)return null;n=s.rowXPath;var l=s.xPath.substring(s.rowXPath.length,s.xPath.length);a=s.elements.map((e=>e.element))}var o=Te(n,!0,t);return null==o?null:(o.firstItem=Oe(o.row.absoluteXPath+l,o.row.absoluteXPath,a,t),H.isNullOrEmpty(o.firstItem.relativeXPath)&&o.row.elements.filter((e=>1==e.childNodes.length&&"#text"==e.childNodes[0].nodeName)).length>0&&(o.firstItem.relativeXPath="./",o.subtrees.push(o.firstItem)),o)},Se=function(e,t){let n=e;var i=G.selectElements(e,t);if(null!=i&&i.length>0){for(let e in $e.dataExtractorMinSimilarTagDomain)if(window.location.href.toLowerCase().indexOf(e)>-1){let n=$e.dataExtractorMinSimilarTagDomain[e];var r=i[0],a=G.selectSingleElement(n[0],t),s=G.selectSingleElement(n[1],t);if(r&&a&&s&&r.parentElement==a.parentElement)return n[1]}n=G.getDynamicXPath(i,!0)??e}return n},Te=function(e,t=!0,n=document){var i=new ue,r=e;t&&(r=Se(e,n));var a=Ue(r,null,n);if(!a||0==a.length)return null;const s=new Map;if(a.forEach((e=>{re.getChildrenSimilarElement(e,r,"","",!0,s)})),!s||0===s.size)return null;const l=Array.from(s.values());return re.calculateRelativeXPathToRows(a,r,l),l.forEach((e=>{var t=Oe(e.absoluteXPath,r,null,n);null!=t&&i.subtrees.push(t)})),i.row=Oe(r,r,a),i},Pe=function(e,t){const n=new Map;re.getChildrenSimilarElement(e,t,"","",!0,n);return Array.from(n.values()).map((e=>e.element))},Ie=function(e,t,n,i=document){const r=new Map;re.getChildrenSimilarElement(e,t,"","",!0,r);const a=Array.from(r.values());for(var s=[],l=0;l<a.length;l++){var o=Oe(a[l].absoluteXPath,n,null,i);null!=o&&s.push(o)}return s},Ce=function(e){let t=[];e.forEach((n=>{if(!t.includes(n)){var i=e.filter((e=>e.name==n.name));if(i.length>1){for(var r=1;r<i.length;r++)i[r].name=i[r].name+r.toString();t.push(...i)}}}))},Re=function(e,t,n=!0,i=document){let r=[],a=e.absoluteXPath,s=e.elements,l="tr"==s[0].tagName.toLowerCase();if(i instanceof DocumentFragment)t.forEach((e=>{var t=De(s,e,i);null!=t&&r.push(...t)}));else{var o=ht.findClassXPathItems(s,t,l,i);o.length>0?o.forEach((e=>{var t=Ue(a+e.classXPath,null,i);t.length<e.elements.length?e.classXPath=null:e.elements=t;var n=De(s,e,i);null!=n&&r.push(...n)})):t.forEach((e=>{var t=De(s,e,i);null!=t&&r.push(...t)}))}let c=ht.findLessCols(r.map((e=>e.data)),l);if(c.length<r.length)for(const e of c.sort(((e,t)=>t-e)))r.splice(e,1);if(!l){const e=ht.findSameCols(r.map((e=>e.data)));let t=[];for(const n of e){const e=n.map((e=>r[e].score));0===ct.sum(e)?t.push(...n.slice(1)):t.push(...e.map(((e,t)=>({index:t,value:e}))).sort(((e,t)=>t.value-e.value)).slice(1).map((e=>n[e.index])))}for(const e of t.sort(((e,t)=>t-e)))r.splice(e,1)}if(l){let e=ht.findSubCol(r.map((e=>e.data)),!0);for(const t of e.sort(((e,t)=>t-e)))r.splice(t,1)}else{r=Ae(a,r,s);let e=ht.findSubCol(r.map((e=>e.data)));for(const t of e.sort(((e,t)=>t-e)))r.splice(t,1);e.indexOf(0)>-1&&(r=Ae(a,r,s))}return n?ke(r,l):r.forEach((e=>{H.isNullOrEmpty(e.name)&&(e.name=Me(e.item.elements[0]))})),Ce(r),r},Xe=function(e,t=null,n=document){var i=Je(e,!0),r=Oe(i,"",null,n),a=new oe,s="";a.item=r,a.ignoreSimilarLoop=!0,null!=e&&(a.name=Me(e),"img"==e.localName?(a.extractProperty=se.src,s=e.src):"a"==e.localName?(s=ht.extractorElementText(e),H.isNullOrEmpty(s)&&(a.extractProperty=se.href,s=e.href)):s=ht.extractorElementText(e));var l=[];return null==t?l.push(s):t.forEach((e=>{l.push(s)})),a.data=l,a};class Fe{constructor(){this.FontSize=0,this.FontWeight=0,this.InnerTextLength=0}}const Oe=function(e,t=null,n=null,i=document){var r=new ce;if(r.elements=null==n?Ue(e,null,i):n,r.absoluteXPath=e,null!=t&&(r.relativeXPath=e.substring(t.length,e.length)),r.elements.length>0){let e="";e=r.elements[0].className instanceof SVGAnimatedString?r.elements[0].className.animVal:r.elements[0].className,r.className=H.isNullOrEmpty(e)||e.startsWith("octopus-rpa")?"":e,r.localName=r.elements[0].localName}return r},Le=function(e){var t={name:e.name,ignoreSimilarLoop:e.ignoreSimilarLoop,extractProperty:e.extractProperty};if(e.item){var n=H.isNullOrEmpty(e.item.classXPath)?e.item.relativeXPath:e.item.classXPath;t.xpath=e.ignoreSimilarLoop?e.item.absoluteXPath:n,t.nodes=e.ignoreSimilarLoop?e.item.absoluteNodes:e.item.relativeNodes}else t.xpath="",t.nodes=[];return t},De=function(e,t,n=document){ht.doc=n;let i=new Array;if(0===e.length)return i;let r="tr"==e[0].tagName;const a=new Array,s=new Array,l=new Array,o=new Array;for(const i of e){let e=Ue(t.classXPath??t.relativeXPath,i,n)[0];if(e){const n=ht.win.getComputedStyle(e),i=new Fe;i.FontSize=Math.min(parseInt(n.fontSize,10),ht.maxFontSize),i.FontWeight=Math.min(parseInt(n.fontWeight,10),ht.maxFontWeight),i.InnerTextLength=Math.min(e.innerText.length,ht.maxInnerTextLength),o.push(i),e.href&&-1===ht.filterLink.indexOf(e.href.toLowerCase())&&-1===$e.excludedLinkStringDict.findIndex((t=>e.href.toLowerCase().indexOf(t)>-1))?e.href.indexOf("#")>-1?a.push(""):a.push(e.href):a.push(""),e.src?e.height<20||e.width<20?s.push(""):s.push(e.src):s.push(""),t.isTags||t.isRichText?l.push(e.innerText):l.push(ht.extractorElementText(e))}else a.push(""),s.push(""),l.push("")}let{stop:c,prefix:u,suffix:m,score:h}={stop:!1,prefix:"",suffix:"",score:1};if(!t.isRetain){let{stop:e,prefix:t,suffix:n}=ht.findCommonStr(l);h=ht.featureScore(o,a.length),l.filter((e=>0===e.length)).length>0&&(h/=10)}let d=!1;if(a.findIndex((e=>e.length>0))>-1&&(!1===c||-1===l.findIndex((e=>e.length>0))||r))(f=new oe).item=t,f.extractProperty=se.href,f.name=$e.resources.defualtFieldNames.link,f.data=a,f.score=h,i.push(f);else if(s.findIndex((e=>e.length>0))>-1){var f;(f=new oe).item=t,f.extractProperty=se.src,f.name=$e.resources.defualtFieldNames.image,f.data=s,i.push(f)}else d=!0;if(!0===r){if(!0===c&&!1===d)return i}else if(c)return i;if(l.findIndex((e=>e.length>0))>=0||0===i.length){var g=new oe;g.name=ht.trimPrefix(u),g.item=t,g.data=l,g.score=h,i.push(g)}return i},Ae=function(e,t,n){let i,r=n.length>1?n[1]:n[0],a=[],s=[],l=t.filter((e=>e.extractProperty==se.text&&(/^h[1-4]$/.test(e.item.localName)||/H[1-4]/.test(e.item.absoluteXPath))));if(1===l.length?i=l[0]:l.length>1&&(l.sort(((e,t)=>$e.hnTags.indexOf(e.item.localName)-$e.hnTags.indexOf(t.item.localName))),i=l[0]),void 0===i){let e=t.filter((e=>{let t=e.item.className.toLowerCase();return t.indexOf("title")>-1&&t.indexOf("subtitle")<0||"tit"===t})),n=[];for(let t of e)n.indexOf(t.item.absoluteXPath)<0&&n.findIndex((e=>t.item.absoluteXPath.startsWith(e)))<0&&n.push(t);if(1===n.length){let e=t.filter((e=>e.extractProperty==se.text&&n.indexOf(e.item.relativeXPath)>-1)).sort(((e,t)=>t.score-e.score));if(e.length>0)for(let t of e){let e=Ue(t.item.relativeXPath,r,r.ownerDocument)[0];if(void 0!==e&&(!e||!(ht.isNumber(e)||ht.isPrice(e)||/^[0-9,.,/]{1,}$/.test(e.innerText)))){i=t;break}}}}if(void 0===i&&(i=t.filter((t=>{if(t.extractProperty!=se.text)return!1;let n=Ue(t.item.relativeXPath,r,r.ownerDocument)[0];if(n&&(ht.isNumber(n)||ht.isPrice(n)||/^[0-9,.,/]{1,}$/.test(n.innerText)||n.innerText&&n.innerText.length<3))return!1;if(void 0===n)return!1;let i=n.innerText.trim();if(i.length>200)return!1;let a=Ue(e+t.item.relativeXPath,null,r.ownerDocument);const s=[];for(const e of a){if(s.indexOf(e.innerText)>-1)return!1;s.push(e.innerText)}if(/[0-9-]{1,}/.test(i)){i.replace(/[0-9- ,()￥¥$元円€]{1,}/g,"").length<5&&(t.score=t.score/10)}return!0})).sort(((e,t)=>t.score-e.score))[0]),i){a.push(i);let l=t.findIndex((e=>e===i));i.name||(i.name=$e.resources.defualtFieldNames.title),s.push(l);let u=t.findIndex((e=>"a"===e.item.localName&&e.item.absoluteXPath===i.item.absoluteXPath));if(u<0){let e=t[l].item.relativeXPath;u=t.findIndex(((n,r)=>{if(n!==i&&"a"===n.item.localName){let n=t[r].item.relativeXPath;return 0===e.indexOf(n)||0===n.indexOf(e)}return!1}))}if(u<0){let t=Ue(i.item.relativeXPath,r,r.ownerDocument)[0],s="";if(t){let l,u=t.parentElement,m=!1;for(;u&&"body"!==u.localName.toLowerCase()&&u!==r;){if(s+="/..","a"===u.localName.toLowerCase()){let e=u.getAttribute("href");e&&!1===e.startsWith("javascript:")&&(s=rt(r,u,ht.doc),m=!0,l=u);break}u=u.parentElement}if(l){const t=ht.doc.baseURI.toLocaleLowerCase();$e.specialRelativeXpathUrls.some((e=>t.indexOf(e)>-1))&&m&&-1!==i.item.relativeXPath.indexOf("descendant-or-self")&&-1!==s.indexOf("descendant-or-self")&&(s=s.replace("descendant-or-self","ancestor-or-self"));var o=i.item;o.absoluteXPath=e+i.item.relativeXPath+s,o.relativeXPath=i.item.relativeXPath+s,o.localName="a";var c=new oe;c.item=o,c.extractProperty=se.href,c.name=$e.resources.defualtFieldNames.titleLink,c.data=_e(c.item.relativeXPath,c.extractProperty,!1,n,!0,ht.doc),a.push(c)}}}u>-1&&(s.push(u),t[u].name=$e.resources.defualtFieldNames.titleLink,a.push(t[u]))}let u=t.filter((e=>"img"===e.item.localName));1===u.length&&(s.push(t.findIndex((e=>e===u[0]))),u[0].name=$e.resources.defualtFieldNames.image,a.push(u[0]));for(let e=0;e<t.length;e++)s.indexOf(e)<0&&a.push(t[e]);return a},ke=function(e,t){let n=0;const i=new Set,r=e=>{let t=e;for(;i.has(t);)n++,t=e+n;return i.add(t),t};for(const n of e){let e=qe(n,t);e=ht.recommendNameFormart(e),e=ht.filterSpecialChar(e),n.name=r(e)}},qe=function(e,t){if(t){let t=e.item.absoluteXPath.substring(0,e.item.absoluteXPath.lastIndexOf(e.item.relativeXPath)),n=t.split("/");if(n.length>2&&n[n.length-2].startsWith("TBODY")){let i=n[n.length-2];n[n.length-2]=i.replace("TBODY","THEAD");let r=ht.getRowThIndex2(e.item.relativeXPath,t),a=Ue(n.join("/"),null,ht.doc);if(a&&a.length>0){let t=a[0];if(t.children[r]){let n=t.children[r].innerText.trim();if(/^[0-9]{1,}/.test(n)&&(n=$e.resources.defualtFieldNames.field),""!==n.trim())return"a"===e.item.localName.toLowerCase()&&e.Name?n+"_"+extractItem.Name:n}}}}if(e.name&&e.name.length>0)return e.name.replace(ht.regexEscapePattern,"");let n=$e.resources.defualtFieldNames.field;if(e&&e.item.className){let t="";e.extractProperty===se.href&&(t="_"+$e.resources.defualtFieldNames.link);let i=e.item.className.replace(ht.regexEscapePattern,"").replace(new RegExp("-","gm"),"_").toLowerCase().split(" "),r=ht.tryTranslate2Chinese(i);if(r){if(r!==ht.tryTranslate2Chinese(["content"]))return r+t;n=r}else n=i[0]+t}if(null!==e.name&&0!==e.name.length&&(e.extractProperty!==se.href||e.name!==$e.resources.defualtFieldNames.link)){let t=e.name.replace(ht.regexEscapePattern,"_");if(t.length>1)return t}let i=Ue(e.item.absoluteXPath,null,ht.doc);if(!i||0==i.length)return $e.resources.defualtFieldNames.field;let r=i[0];try{if(ht.isPublicTimeNode(r))return $e.resources.defualtFieldNames.date;if(ht.isSourceNode(r))return $e.resources.defualtFieldNames.source;if(ht.isAuthorNode(r))return $e.resources.defualtFieldNames.author;if(ht.isPrice(r))return $e.resources.defualtFieldNames.price;if(ht.isNumber(r))return $e.resources.defualtFieldNames.cardNumber}catch(e){}return n},Me=function(e){if(e)try{if(ht.isPublicTimeNode(e))return $e.resources.defualtFieldNames.date;if(ht.isSourceNode(e))return $e.resources.defualtFieldNames.source;if(ht.isAuthorNode(e))return $e.resources.defualtFieldNames.author;if(ht.isPrice(e))return $e.resources.defualtFieldNames.price;if(ht.isNumber(e))return $e.resources.defualtFieldNames.cardNumber}catch{}return $e.resources.defualtFieldNames.field},Be="data-octopus-rpa";class He{static getData(e,t,n=!1,i=document){let r=new Array,a=new Array;H.isNullOrEmpty(t)||(a=G.selectElements(t+`[not(@${Be})]`,i),n&&(a=a.filter((e=>D.isElementPartiallyInViewport(e)))),a.forEach((e=>{e.setAttribute(Be,""),He.rowElements.push(e)})));for(let t of e){let e=_e(t.xpath,t.extractProperty,t.ignoreSimilarLoop,a,!1,i);r.push({name:t.name,data:e.map((e=>H.base64Encode(e)))})}if(0==a.length){r.every((e=>1==e.data.length&&H.isNullOrEmpty(e.data[0])))&&r.forEach((e=>e.data=[]))}return r}static reset(){He.rowElements.length>0&&(He.rowElements.forEach((e=>{e.hasAttribute(Be)&&e.removeAttribute(Be)})),He.rowElements=[])}}He.rowElements=[];const _e=function(e,t=se.text,n=!0,i=[],r=!1,a=document){let s=i.length,l=[];if(n){let n=Ue(e,null,a)[0],i=Ge(n,G.initialLower(t),r);l=new Array(0==s?1:s).fill(i)}else if(0==s)l.push("");else for(let n of i){let i=Ue(e,n,a)[0],s=Ge(i,G.initialLower(t),r);l.push(s)}return l},Ve=function(e,t="",n=[],i=!1){let r=n.length,a=We(e,t,i);return data=new Array(0==r?1:r).fill(a),data},We=function(e,t="",n=!1){let i=t;if(e===le.fixed);else if(e===le.time){let e=new Date;i=G.formateDateTime(e,"yyyy-MM-dd hh:mm:ss.S")}else e===le.pageTitle?i=document.title:e===le.pageUrl?i=window.location.href:e===le.pageSource&&(i=document.documentElement.outerHTML,n&&(i=G.trimHtml(i)));return i=i?i.replace(new RegExp("\n","gm")," "):"",i},Ge=function(e,t,n=!1){let i="";return e&&(t===se.href?i=e.href:t===se.src?i=e.src:t===se.innerHtml?(i=e.innerHTML,n&&(i=G.trimHtml(i))):t===se.outerHtml?(i=e.outerHTML,n&&(i=G.trimHtml(i))):i=t===se.text?ht.extractorElementText(e):"text()"===t?e.textContent:e.getAttribute(t),i=i?i.replace(new RegExp("\n","gm")," "):""),i};class $e{static initDictionary(e,t="zh-CN"){if(e)for(let n in e)if("locale"===n){const n=e.locale[t];$e.resources=n.resources,$e.englishChineseDict=n.englishChineseDict}else $e[n]=e[n]}static isExcludedTagsHost(){return void 0!==$e.excludedTagsDomain.find((e=>document.baseURI.indexOf(e)>-1))}static getNextPageTextIndex(e){if(null==e)return-1;return e=(e=e.replace(" "," ")).replace("\n",""),$e.nextPageTextDict.indexOf(e)}static hasNextPageClass(e){if($e.excludedNextPageClassDict.indexOf(e)>-1)return!1;if($e.nextPageClassDict.indexOf(e)>-1)return!0;let t=e.toLocaleLowerCase();return["page","pagination","link","navigation"].find((e=>t.indexOf(e)>-1))&&["next","right"].find((e=>t.indexOf(e)>-1))&&"right-content-pagination"!==t}static hasActiveClass(e){if($e.activeClassTags.indexOf(e)>-1)return!0;const t=["cur","curr","active","current"],n=e.split(/[-_]/);for(const e of n)if(t.indexOf(e.toLocaleLowerCase())>-1)return!0;return!1}static isInButtonDist(e){if(e.length>18)return!1;if(1===e.length&&!1===/^[0-9]$/.test(e)&&!1===this.chinesePattern.test(e))return!0;const t=["查看"];for(const n of t)if(e.startsWith(n)){if(e.replace(n,"").length<12)return!0}return!/[\(\[][0-9]{1,}[\),\]]/.test(e)&&(void 0!==$e.buttonTextDist.find((t=>e===t))||void 0!==$e.separatorDist.find((t=>e.endsWith(t))))}static hasPageSectionClassRoot(e){const t=e.split(/[-_]/);for(const e of t)if(this.pageSectionClassDict.indexOf(e)>-1)return!0;const n=["pagination"];for(const t of n)if(e.indexOf(t)>-1)return!0;return!1}static isMatchMoreText(e){if(null==e)return!1;const t=e.trim();if(""===t)return!1;if($e.excludedMoreTextDict.indexOf(e)>-1)return!1;if($e.moreTextDict.indexOf(e.trim())>-1)return!0;const n=$e.moreTextDict;for(const e of n)if(t.startsWith(e)){if(t.replace(e,"").length<12)return!0}return!1}static islegalNodeName(e){for(const t of this.illegalNodeNameChars)if(e.indexOf(t)>-1)return!1;return!0}static getLegalNodeName(e){return this.islegalNodeName(e.nodeName)?e.nodeName:'*[name()="'+e.localName+'"]'}static isContainsExcludedPageText(e){return $e.excludedPageText.indexOf(e)>-1||!!/\d/.test(e)}}function Ue(e,t,n=document){try{if(n instanceof DocumentFragment)return gt.selectElements(e,t,n);if(!e)return[];if(t){for(e.startsWith("//")&&!e.startsWith("//descendant-or-self::")&&(e="descendant-or-self::"+(e=e.substring(2)));e.startsWith("/");)e=e.substring(1);""===e&&(e="self::*")}for(;e.endsWith("/");)e=e.substring(0,e.length-1);""===e&&(e="/");const i=n.evaluate(e,t||n,null,XPathResult.ANY_TYPE,null);let r=i.iterateNext();const a=[];for(;r;)a.push(r),r=i.iterateNext();return a}catch(e){return[]}}function ze(e,t){if(e.length!=t.length)return!1;let n=!0;return e.forEach((e=>{-1==t.indexOf(e)&&(n=!1)})),n}function je(e,t){return Ke(e,t)}function Ke(e,t){let n="";t=t.sort(((e,t)=>e-t));let i=[];if(t.length>0){if(0==t[0]){let e=-1;do{if(e+1!=t[0])break;t.shift(),e++}while(t.length>0);i.push("position() > "+(e+1))}t.forEach(((e,t)=>{i.push('position()!="'+(e+1)+'"')})),n="["+i.join(" and ")+"]"}return e+n}function Ze(e,t,n){let i=[],r=t;for(;null!=r&&null!=r&&"BODY"!=r.nodeName.toLocaleUpperCase()&&r!=e;){let t=$e.getLegalNodeName(r),a=0;for(let e=r.previousSibling;e;e=e.previousSibling)e.nodeType===Node.ELEMENT_NODE&&e.nodeName===r.nodeName&&++a;if(r=r.parentElement,n&&(null==r||null==r||"BODY"==r.nodeName.toLocaleUpperCase()||r==e))break;i.unshift(t+"["+ ++a+"]")}return"/"+i.join("/")}function Ye(e,t=document){let n=Ue(e,void 0,t);return!(!n||1!=n.length)}function Je(e,t=!0){const n=[];for(;e&&e.nodeType===Node.ELEMENT_NODE;e=e.parentElement){let i=0;const r=$e.getLegalNodeName(e);if("BODY"===r)return n.length?"//body/"+n.join("/"):"//body";if(t&&e.id)return n.splice(0,0,r.toLowerCase()+'[@id="'+e.id+'"]'),"//"+n.join("/");for(let t=e.previousSibling;t;t=t.previousSibling)t.nodeType===Node.ELEMENT_NODE&&t.nodeName===e.nodeName&&++i;const a=r.toLowerCase().replace('"',""),s="["+(i+1)+"]";n.splice(0,0,a+s)}return n.length?"/"+n.join("/"):""}function Qe(e){if(null===e)return"";const t=[];let n=e.firstChild;for(;n;){if(n.nodeType===Node.TEXT_NODE&&null!==n.textContent){const e=n.textContent.trim();e.length>0&&t.push(e)}n=n.nextSibling}return t.join(" ").trim()}function et(e,t=!1,n=!1){if("body"===e.localName.toLowerCase())return"/BODY";let i="";if(e.id&&(i="//"+$e.getLegalNodeName(e)+'[@id="'+e.id+'"]',t&&(i+='[not(contains(@style,"display: none;"))]'),Ye(i,e.ownerDocument)))return i;if(e.className){if(i="//"+$e.getLegalNodeName(e)+'[contains(@class,"'+e.className+'")]',Ye(i,e.ownerDocument))return i;if(i="//"+$e.getLegalNodeName(e)+'[@class="'+e.className+'"]',t){let t=Qe(e);t.indexOf("\n")>-1&&(t=t.substring(0,t.indexOf("\n"))),$e.getNextPageTextIndex(t)>-1&&(i+='[contains(string(),"'+t+'")][not(@disabled)]');let n=Ue(i,null,e.ownerDocument);if(n&&n.length>0)for(let t=0;t<n.length;t++){if(n[t]===e)return"("+i+")["+(t+1)+"]"}}if(Ye(i,e.ownerDocument))return i;if(i=et(e.parentElement,t)+"/"+$e.getLegalNodeName(e)+'[@class="'+e.className+'"]',Ye(i,e.ownerDocument))return i}if(t){if(e.title&&$e.getNextPageTextIndex(e.title)>-1&&(i="//"+$e.getLegalNodeName(e)+'[normalize-space(@title)="'+e.title+'"][not(@disabled)]',Ye(i,e.ownerDocument)))return i;let n=Qe(e);if(n&&n.length<9){if(i="//"+$e.getLegalNodeName(e)+'[normalize-space(text())="'+n+'"][not(@disabled)]',$e.getNextPageTextIndex(n)>-1||$e.moreTextDict.indexOf(n)>-1){let t=Ue(i,void 0,e.ownerDocument);if(1===t.length)return i;for(let n=0;n<t.length;n++){if(t[n]===e)return"("+i+")["+(n+1)+"]"}}if(e.parentElement.id&&(i="//"+$e.getLegalNodeName(e.parentElement)+'[@id="'+e.parentElement.id+'"][not(contains(@style,"display: none;"))]/'+$e.getLegalNodeName(e)+'[text()="'+e.innerText+'"]',Ye(i,e.ownerDocument)))return i;if(e.innerText&&($e.getNextPageTextIndex(e.innerText.trim())>-1||$e.moreTextDict.indexOf(e.innerText.trim())>-1)&&(i=et(e.parentElement)+"/"+$e.getLegalNodeName(e)+'[text()="'+e.innerText+'"][not(@disabled)]',Ye(i,e.ownerDocument)))return i;if(e.parentElement.className){if(e.innerText&&($e.getNextPageTextIndex(e.innerText.trim())>-1||$e.moreTextDict.indexOf(e.innerText.trim())>-1)&&(i="//"+$e.getLegalNodeName(e.parentElement)+'[@class="'+e.parentElement.className+'"]/'+$e.getLegalNodeName(e)+'[text()="'+e.innerText+'"][not(@disabled)]',Ye(i,e.ownerDocument)))return i;i="//"+$e.getLegalNodeName(e.parentElement)+'[@class="'+e.parentElement.className+'"]/'+$e.getLegalNodeName(e)+"[last()][not(@disabled)]";let t=Ue(i,void 0,e.ownerDocument);if(t&&1===t.length&&t[0]===e)return i}if("li"===e.parentElement.localName){i=et(e.parentElement)+"/"+$e.getLegalNodeName(e)+"[last()][not(@disabled)]";let t=Ue(i,void 0,e.ownerDocument);if(t&&1===t.length&&t[0]===e)return i}}let r=et(e.parentElement,t);if(e.className){if(i=r+"/"+$e.getLegalNodeName(e)+'[@class="'+e.className+'"]',Ye(i,e.ownerDocument))return i}else if(i=r+"/"+$e.getLegalNodeName(e),Ye(i,e.ownerDocument))return i;if(e.parentElement.className){i="//"+$e.getLegalNodeName(e.parentElement)+'[@class="'+e.parentElement.className+'"]/'+$e.getLegalNodeName(e)+"[last()]";let t=Ue(i,void 0,e.ownerDocument);if(t&&1===t.length&&t[0]===e)return i}}if(n){let t=0,n=e.previousElementSibling;for(;n;)n.localName===e.localName&&t++,n=n.previousElementSibling;if(i=et(e.parentElement)+"/"+$e.getLegalNodeName(e)+"["+(t+1)+"]",Ye(i,e.ownerDocument))return i}return Je(e)}function tt(e,t,n,i){let r=e.substring(e.indexOf('"')+1,e.lastIndexOf('"')),a="";return a=r?e.replace(r,"XxX"):e,{xpath:e,pattern:a,className:r,orgXPath:t,isTags:n,isRichText:i}}function nt(e){let t=new Map;t.set(/^pic img-cms_fragment_[0-9]{1,} img-wrap$/,"pic"),t.set(/^diggnum (un)?answered$/,"diggnum");for(let n of t.keys())if(n.test(e))return t.get(n);return e}function it(e){let t=$e.specialXPathDict;for(let n of Object.keys(t))if(n===e)return t[n];return e}function rt(e,t,n){let i=[],r=t;for(;null!=r&&null!=r&&"BODY"!=r.nodeName.toLocaleUpperCase()&&r!=e;){let t=r.nodeName,a=0;if(r.className){let a=r.className.trim().replace(new RegExp("\n","gm"),"");a.indexOf("clearfix")>-1&&(a=a.substring(0,a.indexOf("clearfix")-1));for(let e=0;e<$e.activeClassTags.length;e++){let t=$e.activeClassTags[e];if(a===t){a="";break}a=a.replace(new RegExp("\\s+"+t,"gm")," "),a=a.replace(new RegExp(t+"\\s+","gm")," "),a=a.trim()}if(a.length>0&&!1===/\d{3,}$/.test(a)&&a.indexOf(",")<0){a=nt(a);let s="",l=[],o=a.split(/[ \n]/),c=o.findIndex((e=>/[0-9_-]{4,}$/.test(e)));if(c>0)for(let e=o.length-1;e>=c;e--)o.splice(e,1);else l.push(a);if(o.length>1)for(let e=0;e<o.length-1;e++){let t=o.filter(((t,n)=>n<o.length-1-e)),n=t.join(" ").trim();l.indexOf(n)<0&&l.push(n);let i=o.filter(((t,n)=>n>e)),r=i.join(" ").trim();l.indexOf(r)<0&&l.push(r)}l=l.filter((e=>0==$e.xPathIgnoreClassNames.filter((t=>e.indexOf(t)>-1)).length));for(let a of l){let l="descendant-or-self::"+t+'[contains(@class,"'+a+'")]',o=Ue(l,e,n);if(o&&o.length>0){let i=[];for(let e of o)i.indexOf(e.parentElement)<0&&i.push(e.parentElement);if(i.length>1){let i="descendant-or-self::"+t+'[@class="'+a+'"]';o=Ue(i,e,n);let s=[];for(let e of o)s.indexOf(e.parentElement)<0&&s.push(e.parentElement);if(0===s.length||s.length>1){if(r.parentElement===e||!r.parentElement.className||!1!==/[0-9_-]{4,}$/.test(r.parentElement.className))continue;{let i="descendant-or-self::"+r.parentElement.localName+'[@class="'+r.parentElement.className+'"]/'+t+'[contains(@class,"'+a+'")]',s=Ue(i,e,n);if(1!==s.length)continue;l=i,o=s}}else l=i}let s=o.findIndex((e=>e===r));s>-1&&(l+=0===s?"":"["+(s+1)+"]")}s=a,i.unshift(l);break}if(s)break}}if("p"===r.localName||"h1"===r.localName||"h2"===r.localName){let t="descendant-or-self::"+r.localName,a=Ue(t,e,n);if(a&&1===a.length){i.unshift(t);break}}for(let e=r.previousSibling;e;e=e.previousSibling)e.nodeType===Node.ELEMENT_NODE&&e.nodeName===t&&++a;r=r.parentElement,i.unshift(t+"["+ ++a+"]")}if(i.length>1&&i[0].startsWith("descendant-or-self::")){let r="/"+i[0]+"//"+i[i.length-1],a=Ue(r,e,n);if(a&&1===a.length&&a[0]===t)return r}let a="/"+i.join("/");return a=it(a),a}function at(e){let t=[],n=e;for(;n;){if(n.children.length>1)return;"a"===n.localName?t.push("$a$"):t.push(n.localName),n=n.firstElementChild}return t.length>0?t.join("/"):void 0}function st(e){let t=[/((\d{4}|\d{2})(\-|\/|\.)\d{1,2}(\-|\/|\.)\d{1,2})|(\d{4}年\d{1,2}月\d{1,2}日)|\d{1,2}分钟前|\d{1,2}小时前/,/^[(（]?([0-9]{2,4}-)?[0-9]{2,4}[-/][0-9]{1,2} [0-2][0-9]:[0-6][0-9]([0-9]{2})?[)）]?$/,/^[今昨前]天 [0-2]{0,1}[0-9]:[0-9]{1,2}(:[0-9]{1,2}){0,1}$/];for(let n of t)if(n.test(e))return!0;return!1}function lt(e){let t=[],n=!1,i=0,r=[],a=0;for(let s of e.childNodes){if(3===s.nodeType){if(""!==s.textContent.trim()){if(void 0!==$e.tagsSeparatorDist.find((e=>e===s.textContent.trim())))continue;if(void 0===$e.tagsTitleDist.find((e=>e===s.textContent.trim()))||!1===n)return!1}continue}if(1!==s.nodeType)continue;if("svg"===s.nodeName.toLocaleLowerCase())continue;let e=s.innerText;if(null===e||""===e)continue;if(i++,void 0!==$e.tagsTitleDist.find((t=>t===e))&&!1===n&&(n=!0,1===i))continue;let l=at(s);if(t.indexOf(l)<0&&t.push(l),r.indexOf(e.trim())>-1)return!1;if(r.push(e.trim()),/^[0-9]{1,}$/.test(e)&&a++,a>2)return!1;if(st(e))return!1;if(t.length>1)return!1}if(1===t.length&&void 0!==t[0]){let n=0;for(const t of e.children)t.innerText&&n++;if(n>18)return!1;let i=t[0];if(n>1&&i.indexOf("$a$")<0)return 2!==e.children.length||"label"!==e.children[0].className||"label"===e.children[1].className;if(1===e.children.length&&void 0!==$e.adKeywordsDist.find((t=>e.firstElementChild.innerText===t)))return!1;if(e.children.length>1&&n>1){let t=void 0!==$e.tagsClassNameDist.find((t=>e.className.toLocaleLowerCase().indexOf(t)>-1)),n=e;for(;n&&!1===t;)t=void 0!==$e.tagsClassNameDist.find((e=>n.className.toLocaleLowerCase().indexOf(e)>-1)),n=n.firstElementChild;return t}}return!1}function ot(e){let t=$e.chinesePattern.test(e)?e.replace(/\s/g,""):e.trim(),n=/[\u2E80-\u9FFF]+/.test(t)?12:64;if(t.length>n)return!1;if(/[0-9]{2,}/.test(e))return!1;const i=$e.commonLableSeparator.findIndex((e=>t.endsWith(e)&&e!==t));if(i>-1){let e=t.replace(new RegExp($e.commonLableSeparator[i],"gm"),"");return""!==e&&!($e.excludedLableTitleDict.findIndex((t=>t===e))>-1)}return $e.lableTitleSymbolDict.forEach((e=>{let n=t.split(e);for(let e of n)if(e.length>0){t=e;break}})),!(t.length>6&&$e.excludedLableTitleSymbolDict.findIndex((e=>t.indexOf(e)>-1))>-1)&&($e.commonLableTitleSuffixDict.findIndex((e=>t.endsWith(e)))>-1||($e.commonLableTitlePrefixDict.findIndex((e=>t.startsWith(e)))>-1||$e.headerTextDict.findIndex((e=>t===e))>-1))}$e.numRegex=/^([\[【])?\d+([】\]])?$/,$e.chinesePattern=/[\u2E80-\u9FFF]+/,$e.prevPageTextDict=["上一页","<上一页","< 上一页 ","← 上一页","<<上一页","<< 上一页","[上一页]","上页","<前页","前页","前一页","‹","<","<<","Prev","prev","prev page","Prev Page","< Prev"],$e.nextPageTextDict=["下一页","下一页>","下一页 >","下一页 →","下一页>>","下一页 >>","[下一页]","下一页","下页","后页>","后页","下一页→","下一页»","next page","Next Page","Next page","Next page→","Next >","Next>>","next>>","Next","next","›",">",">>","»"],$e.excludedNextPageTextUrl=["://twitter.com/spacex"],$e.excludedLoadMorePageURL=["://twitter.com/spacex"],$e.scrollListNeedRepeatPageURL=["://twitter.com/spacex"],$e.excludedPageText=["第一页","最后一页","下一首"],$e.nextPageClassDict=["next","Next","NEXT","nextpage","nextPage","next-page","pn-next","page_next","Pager-item","default_pgNext","icon-btn_right","page-next","laypage_next","pager-next","nextpostslink","nxt","ant-pagination-next","pages-next","pg-next","icon-angle-right","ui-icon-seek-next"],$e.excludedNextPageClassDict=["next-pagination-ellipsis"],$e.activeClassTags=["cur","hover","active","current","uk-active","selected","red","bg-red","index-pages-select","Pager-item--on","right_page_aOn","ebayui-pagination__li--selected"],$e.listTags=["li"],$e.skipNodes=["form","input","style","script","noscript","applet","object","link"],$e.moreTextDict=["加载更多","+加载更多","查看更多......","查看更多"],$e.excludedMoreTextDict=["阅读全文","展开阅读全文"],$e.excludedMoreClassDict=["caret-arrow"],$e.specialXPath="//*/descendant-or-self::*[(string-length(text()) > 0 and string-length(text()) < 16) or contains(@class,'Page') or contains(@class,'page') or contains(@class,'next') or contains(@class,'nxt') or contains(@title,'页') or contains(@class,'right')]",$e.moreXPath="//*[(contains(text(),'更多') or contains(text(),'再显示') or contains(@class,'more')  or contains(@id,'more')) and string-length(normalize-space(text())) < 20]",$e.excludedAdDomain=["://search.jd.com/",".58.com/job/","://s.1688.com/"],$e.specialRelativeXpathUrls=["://www.amazon.co.jp/"],$e.specialLoopXpathUrls=["://www.homes.co.jp"],$e.adDomain=["://iis1.deliver.ifeng.com/","://pos.baidu.com/","://sspservice.ad-survey.com/","://g.163.com/","://p3.ssl.qhimgs0.com/"],$e.headerStyles=["font-size: 9pt;font-weight:bold;color: #3D3D3D;font-family: 宋体;line-height: 180%;background-color:#e9ede5;","font-size: 17px;font-weight:bold;color: #355e92;font-family: 微软雅黑;line-height: 180%;background-color:#e4f0f3; height:40px;"],$e.headerTextDict=["姓名","编号","所属地区","号码","标题","发布日期","索引号"],$e.hnTags=["h1","h2","h3","h4","h5","h6"],$e.resources={defualtFieldNames:{title:Z.get("defualtFieldNames.title"),titleLink:Z.get("defualtFieldNames.titleLink"),image:Z.get("defualtFieldNames.image"),field:Z.get("defualtFieldNames.field"),link:Z.get("defualtFieldNames.link"),cardNumber:Z.get("defualtFieldNames.cardNumber"),price:Z.get("defualtFieldNames.price"),date:Z.get("defualtFieldNames.date"),source:Z.get("defualtFieldNames.source"),author:Z.get("defualtFieldNames.author"),tag:Z.get("defualtFieldNames.tag"),keywords:Z.get("defualtFieldNames.keywords"),pubTime:Z.get("defualtFieldNames.pubTime"),text:Z.get("defualtFieldNames.text")}},$e.englishChineseDict={zhaiyao:Z.get("defualtFieldNames.abstract"),author:Z.get("defualtFieldNames.author"),summary:Z.get("defualtFieldNames.overview"),ding:Z.get("defualtFieldNames.upvote"),cai:Z.get("defualtFieldNames.downvote"),comment:Z.get("defualtFieldNames.comment"),content:Z.get("defualtFieldNames.content"),price:Z.get("defualtFieldNames.price")},$e.buttonTextDist=["查看","加入","点击","回复","追踪","提出"],$e.excludedSeparatorTextDist=["-","o"],$e.separatorDist=["："],$e.excludedSeparatorUrls=["://loco.yahoo.co.jp"],$e.regularRules={priceReg:"/^[$,￥][0-9,,.]{1,}$|^[0-9,,.]{1,}[元]$/"},$e.specilScrollDomainDist=["://m.haodf.com/"],$e.specialXPathDict={'/descendant-or-self::DIV[contains(@class,"tn-wrapper")]/DL[1]/DT[1]/A[1]':'/descendant-or-self::DIV[contains(@class,"tn-wrapper")]/DL[1]/DT[1]/A[not(@class)]','/descendant-or-self::DIV[contains(@class,"tn-wrapper")]/DL[1]/DT[1]/A[2]':'/descendant-or-self::DIV[contains(@class,"tn-wrapper")]/DL[1]/DT[1]/A[not(@class)]','/descendant-or-self::H2[contains(@class,"ContentItem-title")]/A[1]':'/descendant-or-self::H2[contains(@class,"ContentItem-title")]//A[1]'},$e.ajaxDomainsDist=[".fangdd.com/zufang/",".fangdd.com/loupan/"],$e.excludedTagsDomain=["://so.youku.com/search_video/"],$e.tagsClassNameDist=["tag","keyword","lable","tab","properties"],$e.excludedTagsClassNames=["datagnr","datatag"],$e.tagsTitleDist=["标签","标签："],$e.tagsSeparatorDist=["、"],$e.adKeywordsDist=["广告","推广"],$e.btnClassDist=["btn_bg"],$e.excludedLinkStringDict=["://service.weibo.com/share/"],$e.commonLableSeparator=[":","：","-"],$e.excludedLableTitleDict=["上一篇","下一篇"],$e.commonLableTitleSuffixDict=["名称","日期","编号","时间","历程","备案号","类型","网址","标识码","总量","地区","行业","品目","单位","区域","售价","地点","金额","联系方式","联系人"],$e.commonLableTitlePrefixDict=["是否"],$e.lableTitleSymbolDict=["(","（","["],$e.excludedLableTitleSymbolDict=["，","。","; "],$e.excludedSectionClassDict=["head","foot","banner","bottom"],$e.excludeNoLevelLimitSectionClassDict=["bar","menu","nav","crumb","article"],$e.ajaxClassDist=["default_pgBtn default_pgNext"],$e.pageSectionClassDict=["pagination"],$e.illegalNodeNameChars=[":","*","!"],$e.excludedIdentitiesDict=["recommend","breadcrumb","bread","links","share","hot","login","logout","register"],$e.titleTagsDict=["h1","h2","h3","label"],$e.titleKeywordsDict=["title","heading"],$e.dataExtractorMinSimilarTagDomain={"://weibo.com":['//div[@id="scroller"]/div[1]/div','//div[@id="scroller"]/div[1]/div/div/article'],"://s.weibo.com":['//div[@id="scroller"]/div[1]/div','//div[@id="scroller"]/div[1]/div/div/article']},$e.xPathIgnoreClassNames=["octopus-rpa-stateMachine-selected","octopus-rpa-stateMachine-preselected","octopus-rpa-stateMachine-highlight","octopus-rpa-stateMachine-verify"];class ct{static last=function(e){return e[e.length-1]};static sum=function(e){return e.reduce?e.reduce(((e,t)=>e+t),0):0};static groupBy=function(e,t){return e.reduce?e.reduce(((e,n)=>((e[n[t]]=e[n[t]]||[]).push(n),e)),{}):{}};static binarySearch=function(e,t,n){let i=0,r=this.length-1;for(;i<=r;){const e=~~(i+r>>1);(n?n(i,e,t):this[e]>=t)?r=e-1:i=e+1}return r}}class ut{static isRichTextElement(e){const t=e.localName.toLocaleLowerCase();if("svg"===t||"script"===t||"style"===t)return!1;if("yt-formatted-string"===t&&"metadata-snippet-text style-scope ytd-video-renderer"===e.className)return!0;if("figcaption"===t||"time"===t)return!0;if("field-formatter"===t)return!0;if("reviewBody"===e.getAttribute("itemprop"))return!0;if(e.className&&"price-group"===e.className&&"true"===e.getAttribute("aria-hidden"))return!0;if(e.className&&"search-result-product-shipping-details gridview"===e.className)return!0;if("summary"===e.className&&e.parentElement&&"jobsearch-SerpJobCard unifiedRow row result clickcard"===e.parentElement.className)return!0;if("jobtitle turnstileLink "===e.className)return!0;if("desc des_p"===e.className)return!0;if("summary"===e.className&&e.parentElement&&"news-item"===e.parentElement.className)return!0;if("result__url js-result-extras-url"===e.className)return!0;if("sameSize"===e.className&&"td"===e.localName)return!0;if("mod-date"===e.className&&"span"===e.localName)return!0;const n=e.getAttributeNames();for(const e of n)if(e.indexOf("address")>-1)return!0;const i=Ue("//descendant-or-self::*[count(br)>3]",e,e.ownerDocument);if(i&&1===i.length&&i[0]===e)return!0;if("dd"===t){let t=0;for(const n of e.children){const e=n.getAttributeNames();for(const n of e)if(n.indexOf("address")>-1){t++;break}}if(t>1)return!0}if("article"===t||"dd"===t||e.className&&e.className.toLowerCase().indexOf("text")>-1||e.getAttribute("style")){if(e.children.length<5)return!1;if(e.innerText.length<500)return!1;let t=0,n=0,i=0;for(const r of e.children)"p"===r.localName?t++:"br"===r.localName&&i++,r.getAttribute("style")&&n++;if(t/e.children.length>.5)return!0;if(n/e.children.length>.6)return!0;if(i/e.children.length>.5)return!0}if(e.firstChild&&e.firstChild.nodeType===Node.TEXT_NODE&&""!==e.firstChild.textContent.trim()){if(e.childNodes.length<6)return!1;let t=0,n=0;for(const i of e.childNodes)i.nodeType===Node.TEXT_NODE&&(t++,i.textContent.indexOf(",")>-1&&n++);if(n>6)return!0}return!1}}class mt{CloumnFilter(e){throw new Error("Method not implemented.")}isAdUrl(e){if(!e)return!1;let t=$e.adDomain.find((t=>e.indexOf(t)>-1));return null!=t}isAdElement(e){if($e.excludedAdDomain.find((t=>e.baseURI.indexOf(t)>-1)))return!1;if(e.innerText&&void 0!==$e.adKeywordsDist.find((t=>e.innerText.trim()===t)))return!0;if("a"===e.localName){let t=e.href;if(this.isAdUrl(t))return!0}if("img"===e.localName||"iframe"===e.localName){let t=e.src;if(this.isAdUrl(t))return!0}if("tr"===e.localName.toLowerCase()&&$e.headerStyles.indexOf(e.getAttribute("style"))>-1)return!0;if("tr"===e.localName.toLowerCase()){let t=0;for(let n of e.children)n.innerText.length<6&&ot(n.innerText)&&t++;if(t>2)return!0}if(e.className&&e.className.indexOf&&(e.className.indexOf("mod-banner mod-bigword ")>-1||e.className.indexOf("thread_top_list_folder")>-1))return!0;let t=0;for(let n of e.children){if(1==n.nodeType&&this.isAdElement(n))return!0;n.innerText&&n.innerText.length<6&&ot(n.innerText)&&t++}return t>1&&t>e.children.length/2}}class ht{static extractorElementText(e){let t="";if(e)if(e instanceof HTMLInputElement)t=e.value??"";else{const n=e.innerText??"",i=e.textContent??"";t=n.length>=i.length?n:i}return t.replace(/(^\s*)|(\s*$)/g,"")}static findClassXPathItems(e,t,n,i=document){let r=[],a=[],s=[],l=[];for(const o of t){const t=new Map;for(const c of e){let e=Ue(o.relativeXPath,c,i)[0];if(void 0===e)continue;if(e.parentElement&&void 0!==s.find((t=>t===e.parentElement))){void 0===s.find((t=>t===e))&&s.push(e);continue}let u=!1;if(e.className&&void 0!==$e.tagsClassNameDist.find((t=>e.className.toLowerCase().indexOf(t)>-1))&&!$e.excludedTagsClassNames.includes(e.className.toLowerCase()))if(lt(e))u=!0;else{let t=e.parentElement;for(let n=0;n<4;n++){if(lt(t)){e=t,u=!0;break}t=t.parentElement}}if(u){if(void 0!==s.find((t=>t===e)))continue;s.push(e)}let m=!1,h=n?o.relativeXPath:rt(c,e,i);if(ut.isRichTextElement(e))-1===l.indexOf(o.relativeXPath)&&(l.push({relativeXPath:o.relativeXPath,classXPath:h}),m=!0);else if(l.findIndex((e=>{if(o.relativeXPath.startsWith(e.relativeXPath)&&o.relativeXPath!==e.relativeXPath){return Ue(e.relativeXPath,c,i)[0]===Ue(e.classXPath,c,i)[0]}return!1}))>-1)continue;h===o.relativeXPath?t.has(h)?t.set(h,t.get(h)+1):t.set(h,1):h&&-1===r.indexOf(h)?(r.push(h),a.push(tt(h,o.relativeXPath,u,m))):u&&(a.find((e=>e.xpath===h)).isTags=u)}for(const e of t.keys())t.get(e)>1&&a.push(tt(o.relativeXPath,o.relativeXPath,!1,l.findIndex((e=>e.richtextXPaths===o.richtextXPaths))>-1));if(a.length>200)break}let o=[],c=ct.groupBy(a,"pattern");for(let e in c){let t=c[e],n=t.filter((e=>t.findIndex((t=>{let n=e.className.split(" ");return t!==e&&n.indexOf(t.className)>-1}))<0));o.push(...n)}let u=a.filter((e=>!0===e.isTags)),m=ct.groupBy(o,"orgXPath"),h=[];for(let e of t){let t=m[e.relativeXPath];t&&(u.length>0&&(t=t.filter((e=>u.find((t=>!(e.orgXPath!==t.orgXPath&&e.orgXPath.startsWith(t.orgXPath))))))),t&&t.length>0&&h.push(...t))}let d=[],f=ct.groupBy(h,"orgXPath");for(let e in f){let n=f[e][0];var g=t.filter((t=>t.relativeXPath==e));for(let e of g)e.className=n.className,e.classXPath=n.xpath,e.isTags=n.isTags,e.isRichText=n.isRichText,e.pattern=n.pattern,d.push(e)}return d}static longestCommonPrefix(e){if(!e)return"";for(let t=0;t<e[0].length;t++){const n=e[0][t];for(let i=1;i<e.length;i++)if(t===e[i].length||e[i][t]!==n)return e[0].substring(0,t)}return e[0]}static findAdRows(e){if(0==e.length)return[];let t=[],n=0;for(let i of e)ht.dataFilter.isAdElement(i)&&t.push(n),n++;return t}static findEmptyRows(e){if(!e[0])return[];const t=[];for(let n=0;n<e[0].length;n++){let i=0;for(let t=0;t<e.length;t++)0===e[t][n].length&&i++;(i===e.length||e.length>5&&i>e.length-2)&&t.push(n)}return t}static fixRowXPathByElements(e,t,n=document){let i=Ue(e,void 0,n),r=i[0].localName,a=[],s=[],l=[],o=[],c=[],u=[],m=[];if(i.forEach(((e,n)=>{let i=e.className?e.className.trim():"";t.indexOf(n)>-1?(s.push(e),c.push(i),-1===u.indexOf(i)&&u.push(i)):(a.push(e),l.push(i),m.push(e.id),-1===o.indexOf(i)&&o.push(i))})),1===u.length&&-1===o.indexOf(u[0])){let t=e+'[not(contains(@class,"'+u[0]+'"))]',i=Ue(t,void 0,n);if(ze(a,i))return t}if(1===o.length&&-1===u.indexOf(o[0])){let t=e+'[contains(@class,"'+o[0]+'")]',i=Ue(t,void 0,n);if(ze(a,i))return t}let h=l.filter((e=>0===e.length));if(0===h.length){let i=l.reduce(((e,t)=>t.length>e?t.length:e),0),s=0===l.length?"":this.longestCommonPrefix(l);if("resblock-list post_ulog_exposure_scroll has-results"===s&&(s="resblock-list post_ulog_exposure_scroll"),s&&s.length>0&&!this.isExceptionalPrefix(s)){let t=c.length>0?this.longestCommonPrefix(c):"";if(s!=t){let l="//"+r;i===s.length?l+='[@class="'+s+'"]':l+='[contains(@class,"'+s+'")]';let o=Ue(l,void 0,n);if(ze(a,o))return l;if(l=e,i===s.length?l+='[@class="'+s+'"]':l+='[contains(@class,"'+s+'")]',o=Ue(l,void 0,n),ze(a,o))return l;let u=c.reduce(((e,t)=>t.length>e?t.length:e),0);if(t.length==u){let i="//"+r+'[@class!="'+t+'"]',s=Ue(i,void 0,n);if(ze(a,s))return i;if(i=e+'[@class!="'+t+'"]',s=Ue(i,void 0,n),ze(a,s))return i}}}if(i!==s.length){let s=this.LongestCommonSuffix(l);if(s&&s.length>0&&!this.isExceptionalPrefix(s)){if(s!=(t.length>0?this.LongestCommonSuffix(c):"")){let t="//"+r;i===s.length?t+='[@class="'+s+'"]':t+='[contains(@class,"'+s+'")]';let l=Ue(t,void 0,n);if(ze(a,l))return t;if(t=e,i===s.length?t+='[@class="'+s+'"]':t+='[contains(@class,"'+s+'")]',l=Ue(t),ze(a,l))return t}}}if(c.filter((e=>0===e.length)).length===c.length){let t=e+"[@class]",i=Ue(t,void 0,n);if(ze(a,i))return t}}else if(h.length===l.length){if(0===c.filter((e=>0===e.length)).length){let t=e+"[not(@class)]",i=Ue(t,void 0,n);if(ze(a,i))return t}}if(0===m.filter((e=>0===e.length)).length){let t=0===m.length?"":this.longestCommonPrefix(m);if(t&&t.length>0&&!this.isExceptionalPrefix(t)){let i="//"+r+'[contains(@id,"'+t+'")]',s=Ue(i,void 0,n);if(ze(a,s))return i;if(i=e+'[contains(@id,"'+t+'")]',s=Ue(i,void 0,n),ze(a,s))return i}}if(t.length>5&&2===o.length){const t=l.filter((e=>e===o[0])).length;if(1===t){let t=e+'[contains(@class,"'+o[1]+'")]',i=Ue(t,void 0,n);if(ze(a.splice(0,1),i))return t}else if(t===l.length-1){let t=e+'[contains(@class,"'+o[0]+'")]',i=Ue(t,void 0,n);if(ze(a.splice(a.length-1,1),i))return t}}const d=n.baseURI.toLocaleLowerCase();if($e.specialLoopXpathUrls.some((e=>d.indexOf(e)>-1))&&0!==u.length){let t=e+"[",n=u.length;return u.forEach(((e,i)=>{t+='not(contains(@class,"'+e+'"))'+(i!==n-1?" and ":"]")})),t}return je(e,t)}static LongestCommonSuffix(e){if(!e)return"";const t=e[0].length;for(let n=t-1;n>=0;n--){const i=e[0][n];for(let r=1;r<e.length;r++){const a=e[r].length-t+n;if(a<0||e[r][a]!==i)return e[0].substring(n+1)}}return e[0]}static findSubCol(e,t=!1){let n=[];for(let i=0;i<e.length;i++){let r=e[i];if(t){if(r.toString().length/r.length<20)continue}for(let t=i+1;t<e.length;t++){let a=e[t],s=!0,l=0;for(let e=0;e<r.length;e++)""===r[e]||""===a[e]||r[e]===a[e]&&!/^[0-9.]{1,}$/.test(r[e].trim())?l++:r[e].length>20||r[e].indexOf(a[e])<0||/^[0-9.]{1,}$/.test(r[e].trim())&&/^[0-9.]{1,}$/.test(a[e].trim())?s=!1:l++;if(s=l/r.length>.9,s){let e=r.filter((e=>e.length>0)),s=a.filter((e=>e.length>0));if(e.length<s.length){n.indexOf(i)<0&&n.push(i);break}n.indexOf(t)<0&&n.push(t)}}}return n}static trimPrefix(e){let t="";if(e){t=e.replace(new RegExp(" ","gm"),"");for(let e of $e.separatorDist)t=t.replace(new RegExp(e,"gm"),"");t=t.replace(new RegExp(" ","gm"),"").trim()}return t}static featureScore(e,t){if(0===e.length)return 0;const n=e=>e.reduce(((e,t)=>e+t))/t,i=n(e.map((e=>e.FontSize))),r=n(e.map((e=>e.FontWeight)));let a=n(e.map((e=>e.InnerTextLength)));return a>50?a/=5:a>120&&(a=2),(i/ht.maxFontSize*1.65+r/ht.maxFontWeight*.15+a/ht.maxInnerTextLength*.2)/3}static findCommonStr(e){const t=e.filter((e=>e.length>0));if(0===t.length)return{stop:!0,prefix:"",suffix:""};const n=t.reduce(((e,t)=>e+t.length),0);if(0===n)return{stop:!0,prefix:"",suffix:""};let i=ht.longestCommonPrefix(t);for(let e of $e.separatorDist){let t=i.indexOf(e);if(t>-1&&t<i.length-1){i=i.substring(0,t+1);break}}const r=$e.chinesePattern.test(i)?10:20;if(i.length&&i.length>0&&i.length<r&&n===t.length*i.length&&!ht.isExceptionalPrefix(i)&&ht.isInButtonDist(i))return{stop:!0,prefix:"",suffix:""};if(i.trim().length>0){let e=i;for(let t of $e.separatorDist)e=e.replace(new RegExp(t,"gm"),"");if(""===e.trim()&&n===t.length*i.length)return{stop:!0,prefix:"",suffix:""};i=""}if(n===t.length*i.length)return{stop:!1,prefix:"",suffix:""};ht.chinesePattern.test(i)||(i="");let a=ht.LongestCommonSuffix(t);return a=ct.last(a.trim().split(" ")),ht.chinesePattern.test(a)||(a=""),e.forEach(((e,t,n)=>{i.length>0&&(n[t]=n[t].substring(i.length).trimLeft())})),{stop:!1,prefix:i,suffix:a}}static isExceptionalPrefix(e){return!!/^\d/.test(e)}static findLessCols(e,t){const n=[];for(let i=0;i<e.length;i++){let r=e[i],a=[],s=[];if(r.length<4)break;let l=0;for(let e of r)""===e.trim()?l++:(s.indexOf(e)<0&&s.push(e),-1===a.indexOf(e)&&a.push(e));let o=l/r.length;if(1===a.length&&$e.isInButtonDist(a[0]))n.push(i);else{if(!0===t&&(o<.8||r.length-l>5))continue;if(r.length-l>25)continue;(o>.75||!1===t&&1===s.length&&s[0].length<6&&$e.isInButtonDist(s[0])||!1===t&&s.length<4&&void 0===s.find((e=>!$e.isInButtonDist(e))))&&n.push(i)}}return n}static findSameCols(e){const t=[],n=new Set;for(let i=0;i<e.length;i++){if(n.has(i))continue;const r=e[i].toString(),a=[i];for(let t=i+1;t<e.length;t++){let i=!0;i=0===e[t].filter((e=>""!==e&&!1===/^[0-9.]{1,}$/.test(e))).length,r===e[t].toString()&&!1===i&&(n.add(t),a.push(t))}a.length>1&&t.push(a)}return t}static isInButtonDist(e){return $e.isInButtonDist(e)}static filterSpecialChar(e){if(e){const t=/[`~!@#$%^&*()+=|{}':;',\[\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？\s\\-]/g;e=e.replace(t,"")}return e}static recommendNameFormart(e){e.includes("↓");let t=e.replace(ht.xmlTagInvalidPattern,"").replace(new RegExp(" ","gm"),"_").replace(ht.regexEscapePattern,"_");if(t.includes("_")){let e=!1;do{const n=t.replace("__","_");e=n!==t,t=n}while(e);t.startsWith("_")&&(t=t.substr(1)),t.endsWith("_")&&(t=t.substr(0,t.length-1))}return t}static getRowThIndex2(e,t){let n=0,i=Ue(t+"/"+e.split("/")[1],void 0,ht.doc);if(i&&i.length>0){let e,t=i[0];if("TD"!==t.nodeName&&"TH"!==t.nodeName){let n=t;for(;n&&"BODY"!==n.nodeName;){if("TD"===n.nodeName||"TH"===n.nodeName){e=n.previousSibling;break}n=n.parentElement}}else e=t.previousSibling;if(void 0===e)return 0;for(;e;)1!==e.nodeType||"TD"!==e.nodeName&&"TH"!==e.nodeName||n++,e=e.previousSibling}return n}static tryTranslate2Chinese(e){0===ht.englishKeys.length&&(ht.englishKeys=Object.keys($e.englishChineseDict));for(let t of e){let e=this.englishKeys.findIndex((e=>t.indexOf(e)>-1));if(e>-1)return $e.englishChineseDict[this.englishKeys[e]]}}static isNumber(e){return!(e.innerText.length>25)&&/[0-9]{6,}/.test(e.innerText)}static isPrice(e){if(void 0===e)return!1;return/^[\$,￥][0-9,\,.]{1,}$|^[0-9,\,.]{1,}[元]$/.test(e.innerText.replace(" "," "))}static isContainsKey(e,t){for(let n of t)if(e.indexOf(n)>-1)return!0;return!1}}ht.dataFilter=new mt,ht.maxFontSize=40,ht.maxFontWeight=800,ht.maxInnerTextLength=100,ht.win=window,ht.filterLink=["#","javascript:;","javascript:void(0);","javascript:void(0)","javascript:"],ht.chinesePattern=/^[\u2E80-\u9FFF\s]+/,ht.xmlTagInvalidPattern=/_x\w{4}_/g,ht.regexEscapePattern=/[|\\{}()[\]^$+*?.,、】【↓-]/g,ht.englishKeys=[],ht.isPublicTimeNode=e=>{let t=[/((\d{4}|\d{2})(\-|\/|\.)\d{1,2}(\-|\/|\.)\d{1,2})|(\d{4}年\d{1,2}月\d{1,2}日)|\d{1,2}分钟前|\d{1,2}小时前/,/^[(（]?([0-9]{2,4}-)?[0-9]{2,4}[-/][0-9]{1,2} [0-2][0-9]:[0-6][0-9]([0-9]{2})?[)）]?$/,/^[今昨前]天 [0-2]{0,1}[0-9]:[0-9]{1,2}(:[0-9]{1,2}){0,1}$/],n=e.innerText;if(n.length>20)return!1;for(let e of t)if(e.test(n))return!0;let i=["time","date"];for(let t of i){if(e.id&&e.id.indexOf&&e.id.indexOf(t)>-1)return!0;if(e.className&&e.className.indexOf&&e.className.indexOf(t)>-1)return!0}},ht.isAuthorNode=e=>{if("img"===e.localName)return!1;let t=["author","name"],n=["avrtar","avatar","description"];for(let i of t){if(e.id&&e.id.indexOf&&e.id.indexOf(i)>-1&&!ht.isContainsKey(e.id,n))return!0;if(e.className&&e.className.indexOf&&e.className.indexOf(i)>-1&&!ht.isContainsKey(e.className,n))return!0}return!1},ht.isSourceNode=e=>{if("img"===e.localName)return!1;let t=["source"];for(let n of t){if(e.id&&e.id.indexOf&&e.id.indexOf(n)>-1)return!0;if(e.className&&e.className.indexOf&&e.className.indexOf(n)>-1)return!0}return!1};class dt{constructor(e,t,n){this.doc=e,this.xpath=t,this.nodes=n}}class ft{constructor(e,t){this.shadowRoot=e,this.xpath=t}selectElements(){const e=St(this.xpath);return this.shadowRoot.doc.querySelectorAll(e)}selectSingleElement(){const e=St(this.xpath);return this.shadowRoot.doc.querySelector(e)}}class gt{static getShadowRootElement=function(e,t=document){if(-1==e.indexOf("/shadow-root"))return null;t||(t=document);let n=t,i=e,r=[],a=0;for(;;){const t=G.initialLower(i).indexOf("/shadow-root");if(!(t>-1)){let t=e.slice(0,a);const s=new dt(n,t,r);return new ft(s,i)}{a=a+t+"/shadow-root".length;let e=i.slice(0,t);i=i.slice(t+"/shadow-root".length,i.length);let s=i.indexOf("/");s>-1&&(i=i.slice(s,i.length));let l=null;try{if(n==document)l=G.selectSingleElement(e);else{const t=St(e);l=n.querySelector(t)}}catch{return null}if(null==l||!l.shadowRoot)return null;{n=l.shadowRoot,r.unshift(...G.getNodes(l));let e=new B(l,!0);e.name="shadow-root",e.attributes=[],r.unshift(e)}}}};static getShadowRootElementByPoint=function(e,t){let n=document.elementFromPoint(e,t);var i=document;let r="",a=[];for(;n&&n.shadowRoot&&i!=n.shadowRoot;){r+=G.getXPath(n),r+="/shadow-root",a.unshift(...G.getNodes(n));let s=new B(n,!0);s.name="shadow-root",s.attributes=[],a.unshift(s),i=n.shadowRoot,n=n.shadowRoot.elementFromPoint(e,t)}return{shadowRoot:new dt(i,r,a),element:n}};static selectSingleElement=function(e,t=null,n=document){if(n!=document&&!(n instanceof DocumentFragment))return null;null==t&&(t=n);for(var{xpath:e,relativeType:i}=gt.parseXpathWithRelativeInfo(e),{orXpathList:r,single:a,elementIndex:s}=gt.parseOrXpath(e),l=null,o=0;o<r.length;o++){if(-1==e.indexOf("/shadow-root")){const e=St(r[o]);l=t.querySelector(e)}else{const e=gt.getShadowRootElement(r[o],t);if(null==e)continue;l=e.selectSingleElement()}if(l)break}return!H.isNullOrEmpty(i)&&"Children"!=i&&l?gt.getRelativeElement(l,i):l};static selectElements=function(e,t=null,n=document){if(n!=document&&!(n instanceof DocumentFragment))return[];null==t&&(t=n);for(var{xpath:e,relativeType:i}=gt.parseXpathWithRelativeInfo(e),{orXpathList:r,single:a,elementIndex:s}=gt.parseOrXpath(e),l=[],o=0;o<r.length;o++){if(-1==e.indexOf("/shadow-root")){const e=St(r[o]);if(a){var c=null;l=(c=e.indexOf("octopus-uid")>-1?t.querySelector(e):t.querySelectorAll(e)[s-1])?[c]:[]}else l=t.querySelectorAll(e)}else{const e=gt.getShadowRootElement(r[o],t);if(null==e)continue;if(a){c=null;l=(c=e.xpath.indexOf("octopus-uid")>-1?e.selectSingleElement(e.xpath):e.selectElements(e.xpath)[s-1])?[c]:[]}else l=e.selectElements(e.xpath)}if(l.length>0)break}if(!H.isNullOrEmpty(i)&&l.length>0){if("Children"==i){var u=[];return[...l].forEach((e=>{u.push(...gt.getRelativeElement(e,i))})),u}return[...l].map((e=>gt.getRelativeElement(e,i))).filter((e=>null!=e))}return[...l]};static parseOrXpath(e){var t=[];const n=/\(([^)]+)\)\[(\d+)\]/,i=e.match(/\[@octopus-uid="(.*?)"\]\|(.*)\)\[1\](.*?)$/);var r=1;if(i){let e=i[1],a=i[2],s=i[3];const l=a.match(n);l&&(a=l[1],r=l[2]);let o="",c=a.lastIndexOf("/shadow-root");return c>-1&&(o=a.slice(0,c+"/shadow-root".length)),t.push(o+`//*[@octopus-uid="${e}"]`+s),t.push(a+s),{orXpathList:t,single:!0,elementIndex:r}}{const i=e.match(n);return i?(e=i[1],r=i[2],t.push(e),{orXpathList:t,single:!0,elementIndex:r}):(t.push(e),{orXpathList:t,single:!1,elementIndex:r})}}static getRelativeElement=function(e,t){return"Parent"==t?e.parentElement:"Children"==t?[...e.children]:"PrecedingSibling"==t?e.previousElementSibling:"FollowingSibling"==t?e.nextElementSibling:null};static parseXpathWithRelativeInfo=function(e){var t="";for(const[n,i]of Object.entries({Parent:"/..",Children:"/*",PrecedingSibling:"/preceding-sibling::*[1]",FollowingSibling:"/following-sibling::*[1]"}))if(e.endsWith(i)){t=n,e=e.slice(0,e.length-i.length);break}return{xpath:e,relativeType:t}}}class pt{static isIdentifierStart=function(e){return e<48?45===e:e<65?36===e:e<91||(e<97?95===e:e<123)};static isIdentifierChar=function(e){return e<45?36===e:e<48?45===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123))};static isNewLine=function(e){return 10===e||13===e||8232===e||8233===e}}class Et{constructor(e,t){void 0===t&&(t={}),this.label=e,this.beforeExpr=Boolean(t.beforeExpr)}}var xt={};function wt(e,t){void 0===t&&(t={}),t.keyword=e;var n=new Et(e,t);return xt[e]=n,n}const yt={num:new Et("num"),string:new Et("string"),name:new Et("name"),eof:new Et("eof"),bracketL:new Et("["),bracketR:new Et("]"),parenL:new Et("("),parenR:new Et(")"),pipe:new Et("|"),slash:new Et("/"),doubleSlash:new Et("//"),star:new Et("*"),at:new Et("@"),eq:new Et("="),epq:new Et("!="),comma:new Et(","),and:wt("and",{beforeExpr:!0})};var bt=function(){function e(e){this.input=String(e),this.pos=0,this.start=0,this.end=0,this.type=yt.eof,this.value=void 0,this.lastTokStart=this.pos,this.lastTokEnd=this.pos}return e.prototype.raise=function(e,t){var n=new SyntaxError(t+=" ("+e+")");throw n.pos=e,n},e.prototype.next=function(){return this.lastTokEnd=this.end,this.lastTokStart=this.start,this.nextToken()},e.prototype.nextToken=function(){return this.skipSpace(),this.start=this.pos,this.pos>=this.input.length?this.finishToken(yt.eof):this.readToken(this.fullCharCodeAtPos())},e.prototype.readToken=function(e){return pt.isIdentifierStart(e)?this.readWord():this.getTokenFromCode(e)},e.prototype.fullCharCodeAtPos=function(){return this.input.charCodeAt(this.pos)},e.prototype.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:this.raise(this.pos,"Unexpected newline character");default:if(!(e>8&&e<14))break e;++this.pos}}},e.prototype.readTokenSlash=function(){if(47===this.input.charCodeAt(this.pos+1)){var e=this.input.slice(this.pos,this.pos+=2);return this.finishToken(yt.doubleSlash,e)}var t=this.input.slice(this.pos,this.pos+=1);return this.finishToken(yt.slash,t)},e.prototype.readTokenExclamation=function(){if(61===this.input.charCodeAt(this.pos+1)){var e=this.input.slice(this.pos,this.pos+=2);return this.finishToken(yt.epq,e)}this.raise(this.pos,'Unexpected character "'+this.input[this.pos]+'"')},e.prototype.getTokenFromCode=function(e){switch(e){case 40:return++this.pos,this.finishToken(yt.parenL);case 41:return++this.pos,this.finishToken(yt.parenR);case 44:return++this.pos,this.finishToken(yt.comma);case 91:return++this.pos,this.finishToken(yt.bracketL);case 93:return++this.pos,this.finishToken(yt.bracketR);case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber();case 34:case 39:return this.readString(e);case 47:return this.readTokenSlash();case 124:return++this.pos,this.finishToken(yt.pipe);case 42:return++this.pos,this.finishToken(yt.star);case 64:return++this.pos,this.finishToken(yt.at);case 33:return this.readTokenExclamation();case 61:return++this.pos,this.finishToken(yt.eq);default:this.raise(this.pos,'Unexpected character "'+this.input[this.pos]+'"')}},e.prototype.finishToken=function(e,t){this.end=this.pos,this.type=e,this.value=t},e.prototype.readInt=function(e,t){void 0===t&&(t=null);for(var n=this.pos,i=0,r=null===t?1/0:t,a=0;a<r;++a){var s=this.fullCharCodeAtPos(),l=void 0;if((l=s>=97?s-97+10:s>=65?s-65+10:s>=48&&s<=57?s-48:1/0)>=e)break;++this.pos,i=i*e+l}return this.pos===n||null!==t&&this.pos-n!==t?null:i},e.prototype.readNumber=function(){var e=this.pos;null===this.readInt(10)&&this.raise(e,"Invalid Number");var t=this.fullCharCodeAtPos();46===t&&(++this.pos,this.readInt(10),t=this.fullCharCodeAtPos()),69!==t&&101!==t||(43!==(t=this.input.charCodeAt(++this.pos))&&45!==t||++this.pos,null===this.readInt(10)&&this.raise(e,"Invalid number")),pt.isIdentifierStart(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var n=this.input.slice(e,this.pos);return this.finishToken(yt.num,n)},e.prototype.readString=function(e){for(var t="",n=this.start,i=++this.pos;;){this.pos>=this.input.length&&this.raise(n,"Unterminated string");var r=this.fullCharCodeAtPos();if(r===e)break;92===r?(t+=this.input.slice(i,this.pos),t+=this.readEscapedChar(),i=this.pos):(pt.isNewLine(r)&&this.raise(n,"Unterminated string"),++this.pos)}return t+=this.input.slice(i,this.pos++),this.finishToken(yt.string,t)},e.prototype.readEscapedChar=function(){var e=this.input.charCodeAt(++this.pos);switch(++this.pos,e){case 110:return"\n";case 114:return"\r";case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.fullCharCodeAtPos()&&++this.pos;case 10:return"";default:return String.fromCharCode(e)}},e.prototype.readWord1=function(){for(var e=this.pos;this.pos<this.input.length;){var t=this.fullCharCodeAtPos();if(!pt.isIdentifierChar(t))break;++this.pos}return this.input.slice(e,this.pos)},e.prototype.readWord=function(){var e=this.readWord1();return xt[e]?this.finishToken(xt[e],e):this.finishToken(yt.name,e)},e.prototype.startNode=function(){return{type:"",start:this.start,end:0}},e.prototype.finishNode=function(e,t){return e.type=t,e.end=this.lastTokEnd,e},e.prototype.parse=function(){return this.nextToken(),this.parseTopLevel()},e.prototype.parseTopLevel=function(){for(var e=[];this.type!==yt.eof;){var t=this.parseStep();e.push(t)}return e},e.prototype.parseStep=function(){var e=this.startNode();return e.axis=this.parseAxis(),e.nodeTest=this.parseNodeTest(),e.predicate=this.parseFilters(),this.finishNode(e,"step")},e.prototype.parseAxis=function(){var e=this.type;return e===yt.doubleSlash?(this.next(),yt.doubleSlash):e===yt.slash?(this.next(),yt.slash):void this.raise(this.start,"Expect an axis")},e.prototype.parseNodeTest=function(){var e=this.startNode(),t=this.type;if(t===yt.star)return e.value="*",this.next(),this.finishNode(e,"nodeTest");if(t===yt.name)return this.parseTag(e);if(t===yt.at){e.value="@";var n=this.startNode();return e.predicate=[n],this.parseAttrFilter(n),this.finishNode(e,"nodeTest")}this.raise(this.start,"Expect a node test")},e.prototype.parseTag=function(e){return e.value=this.value,this.next(),this.type===yt.parenL&&this.raise(this.start,"Unexpected xpath function"),this.finishNode(e,"nodeTest")},e.prototype.parseFilters=function(){for(var e=[];this.eat(yt.bracketL);){var t=this.parsePredicate();if(e.push(t),"positionFilter"!==t.type&&this.eat(yt.and)){var n=this.parsePredicate();e.push(n)}this.expect(yt.bracketR)}return e},e.prototype.parsePredicate=function(){var e=this.startNode();switch(this.type){case yt.at:return this.parseAttrFilter(e);case yt.name:return this.parseMaybeFun(e);case yt.num:return e.position=this.value,this.next(),this.finishNode(e,"positionFilter");default:this.raise(this.start,"Unexpected node predicate")}},e.prototype.parseAttrFilter=function(e){return this.next(),e.attr=this.getName(),this.eat(yt.eq)&&(e.value=this.getString(),e.mode=yt.eq.label),this.eat(yt.epq)&&(e.value=this.getString(),e.mode=yt.epq.label),this.finishNode(e,"attributeFilter")},e.prototype.parseFunction=function(e){return this.next(),e.arg=this.parseBindingList(yt.parenR),this.finishNode(e,"function")},e.prototype.eat=function(e){return this.type===e&&(this.next(),!0)},e.prototype.expect=function(e){return this.eat(e)||this.raise(this.lastTokEnd,'Expect a "'.concat(e.label,'" after, got ').concat(this.type.label))},e.prototype.getName=function(){this.type!==yt.name&&this.raise(this.start,"Expect a valid name");var e=this.value;return this.next(),e},e.prototype.getString=function(){this.type!==yt.string&&this.raise(this.start,"Expect a string");var e=this.value;return this.next(),e},e.prototype.parseBindingList=function(e){for(var t=[],n=!0;!this.eat(e);){n?n=!1:this.expect(yt.comma);var i=this.parseArg();t.push(i)}return t},e.prototype.parseArg=function(){var e=this.startNode();switch(this.type){case yt.name:return this.parseMaybeFun(e);case yt.string:return e.name=this.getString(),this.finishNode(e,"string");case yt.at:return this.next(),e.attr=this.getName(),this.finishNode(e,"attribute");default:this.raise(this.start,"Unexpected function argument")}},e.prototype.parseMaybeFun=function(e){var t;return e.name=this.getName(),"position"===e.name?(this.expect(yt.parenL),this.expect(yt.parenR),this.type===yt.epq?(this.expect(yt.epq),e.mode=yt.epq.label):(this.expect(yt.eq),e.mode=yt.eq.label),(t=this.type)===yt.string?e.position=parseInt(this.value):t===yt.num?e.position=this.value:this.raise(this.start,"Expect a number for position function, got ".concat(this.type.label,".")),this.next(),this.finishNode(e,"function")):"text"===e.name||"local-name"===e.name?(this.expect(yt.parenL),this.expect(yt.parenR),this.expect(yt.eq),(t=this.type)!==yt.string&&this.raise(this.start,"Expect a string for position function, got ".concat(this.type.label,".")),e.value=this.value,this.next(),this.finishNode(e,"function")):this.type===yt.parenL?this.parseFunction(e):this.finishNode(e,"name")},e}();class Nt{static axisToStr(e,t){if(t)return"";if(e===yt.doubleSlash)return" ";if(e===yt.slash)return" > ";throw Error("!Unexpected axis")}static nodeTestToStr(e){return"*"===e.value?"":"@"===e.value?Nt.attrFilterToStr(e.predicate[0]):e.value||""}static attrFilterToStr(e){var t=e.attr;return e.mode==yt.epq.label?e.value?":not([".concat(t,'="').concat(e.value,'"])'):":not([".concat(t,"])"):e.value?"[".concat(t,'="').concat(e.value,'"]'):"[".concat(t,"]")}static xpathFuncToStr(e){switch(e.name){case"contains":return Nt.containsToStr(e);case"position":return Nt.positionToStr(e);case"text":return Nt.textToStr(e);case"not":return Nt.notToStr(e);case"starts-with":return Nt.startsWithToStr(e);case"ends-with":return Nt.endsWithToStr(e);default:throw new Error("Unsupported function: ".concat(e.name))}}static endsWithToStr(e){if(!e.arg)throw new Error("No arguments for function: ".concat(e.name));if("attribute"===e.arg[0].type&&e.arg[1]&&"string"==e.arg[1].type)return"[".concat(e.arg[0].attr,"$='").concat(e.arg[1].name,"']");throw new Error("Unsupported function: ends-With")}static startsWithToStr(e){if(!e.arg)throw new Error("No arguments for function: ".concat(e.name));if("attribute"===e.arg[0].type&&e.arg[1]&&"string"==e.arg[1].type)return"[".concat(e.arg[0].attr,"^='").concat(e.arg[1].name,"']");throw new Error("Unsupported function: starts-With")}static notToStr(e){if(!e.arg)throw new Error("No arguments for function: ".concat(e.name));return"attribute"===e.arg[0].type?e.arg[1]&&"string"==e.arg[1].type?":not([".concat(e.arg[0].attr,"='").concat(e.arg[1].name,"'])"):":not([".concat(e.arg[0].attr,"])"):":not(".concat(Nt.predicateToStr(e.arg[0]),")")}static textToStr(e){return':contains("'.concat(e.value,'")')}static positionToStr(e){return e.mode==yt.epq.label?":not(:nth-of-type(".concat(e.position,"))"):":nth-of-type(".concat(e.position,")")}static containsToStr(e){if(!e.arg)throw new Error("No arguments for function: ".concat(e.name));if(2!==e.arg.length)throw new Error("Expect contains function to have 2 arguments");if("text"===e.arg[0].name)return":contains(".concat(e.arg[1].name,")");if("attribute"===e.arg[0].type)return"[".concat(e.arg[0].attr,"*='").concat(e.arg[1].name,"']");throw new Error("Unsupported function: ".concat(e.arg[0].type))}static predicateToStr(e){switch(e.type){case"positionFilter":return Nt.positionToStr(e);case"attributeFilter":return Nt.attrFilterToStr(e);case"function":return Nt.xpathFuncToStr(e);case"name":return":has(".concat(e.name,")");default:throw new Error("!Unhandled predicate")}}static stepToSel(e,t){return Nt.axisToStr(e.axis,0===t)+Nt.nodeTestToStr(e.nodeTest)+(e.predicate||[]).map(Nt.predicateToStr).join("")}static xpathTocss(e){return e.map(Nt.stepToSel).join("")}}const vt=e=>{var t=e.replace("//descendant-or-self::","//").replace("/descendant-or-self::","//").replace("descendant-or-self::","//");t=t.replace(/substring\((@[a-zA-Z]+), string-length\(\1\) - \d+\)='([^']+)'/g,((e,t,n)=>`ends-with(${t}, '${n}')`));try{var n=new bt(t).parse()}catch(e){throw new Error("Not supported shadow-root xpath")}return n},St=e=>{var t=vt(e);return t.forEach((e=>{if("*"==e.nodeTest.value&&null!=e.predicate){var t=e.predicate.find((e=>"local-name"==e.name));if(t){e.nodeTest.value=t.value;var n=e.predicate.indexOf(t);e.predicate.splice(n,1)}}})),Nt.xpathTocss(t)};
//# sourceMappingURL=merged.js.map