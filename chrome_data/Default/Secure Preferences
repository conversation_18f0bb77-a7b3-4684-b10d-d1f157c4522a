{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.184\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.184\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "ifadjbbjkpjhckanondlpkfapfkfefnj": {"account_extension_type": 0, "ack_prompt_count": 1, "active_bit": false, "active_permissions": {"api": ["alarms", "cookies", "debugger", "downloads", "nativeMessaging", "tabs", "webNavigation", "scripting"], "explicit_host": ["<all_urls>", "http://*/*", "https://*/*"], "manifest_permissions": [], "scriptable_host": ["file:///*", "ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 4097, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"background": {"service_worker": "background.static.js"}, "content_scripts": [{"all_frames": true, "js": ["content.static.js"], "match_about_blank": true, "matches": ["http://*/*", "https://*/*", "ftp://*/*", "file://*/*"], "run_at": "document_start"}], "content_security_policy": {"extension_pages": "default-src 'self'"}, "description": "Add-on for web automation.  provide by OctopusRPA", "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "icons": {"128": "icons/rpa_logo_128.png", "16": "icons/rpa_logo_16.png", "32": "icons/rpa_logo_32.png", "48": "icons/rpa_logo_48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAomVatfK/f23gmi+fbdftcDkd9LLmTLboVJQAm+3oEaYuoO0I0uNT87jGkdK33NLUfk20vu3XTrFyGNFPMxm3hvwIDoc48ouWXU8ZEwQH+iTHNvOwwKCeGontjkniK9q87nsTaL/mLgHOe2C/Fib7DSBPLp8cZ7y7TY/kQRqeLcyaCXhKGBK5uTV/mtsBd8ldKEroJGCAsXMA8cyswoLfq244T1S1M4UgV5Jkv9kbtIvRSVCG/9hmDWDr+fPj9Q0lcNcp1A/WRqSe28DT2JDHtaMoSM9A2nqmnvSLx1qqFv9Z+DfVQr1D1kZtY5yKToLrT75mlNkZfrGD+w1rTYhQcQIDAQAB", "manifest_version": 3, "name": "八爪鱼RPA", "permissions": ["scripting", "debugger", "tabs", "webNavigation", "downloads", "cookies", "nativeMessaging", "alarms"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "0.0.39"}, "path": "ifadjbbjkpjhckanondlpkfapfkfefnj\\0.0.39_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.184\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.184\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "5CE03492A30F50BC363FDC75EA05EC17A8A57C6E5C1AD1FE1A5B547E7BD26627"}, "extensions": {"ui": {"developer_mode": "69F2CE2A1CF6BBB1B2F5156B9A437C90C12087649234AFF1C3EDAAD6FBCB8EA2"}}, "homepage": "7B09F9470CD01300B3985FF4812F26110A77F8AC7C849D77C1D69110A224F964", "homepage_is_newtabpage": "30D7E081B8C091AAC8B0F294729791D99B9F94F12CE0A3A0233B7DC38B1E3AA2", "session": {"restore_on_startup": "0000CC50BE72909C023C1C686DEA7E551A03A63CAA222EBEC2ACA03B8EB73609", "startup_urls": "7C1244154C09E1839C9D4DEF0A5390D896208A546EB09A68E6B6D68389EBE598"}}, "browser": {"show_home_button": "748CB1DA733762D6425212BCDAC42C28528249C333A1D1001EF659C65F9E02F8"}, "default_search_provider_data": {"template_url_data": "91FBDA9E1973D77E21D115D189B702B2506C4B4F45E0F472B79ED5DF390250D3"}, "enterprise_signin": {"policy_recovery_token": "B2A74935ACEE8891ED2ADA3843D025256105B4D8851AF8D7838B65BFEB1EFDEB"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "81D55B210BA23BF4DAFF8FE1CC80A74A9800C7163BE618A9E4AEF3F845451636", "fignfifoniblkonapihmkfakmlgkbkcf": "B3F2256ECAC65901F41BBED45E5149F0832C04D19B15D6510A96B7AA51F9FCBD", "ifadjbbjkpjhckanondlpkfapfkfefnj": "A6849068C99F6A7E8EB07D19F91A3051282A59C03EBE2B0E7A623F48BF394744", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "1B55EA47E0EEF008D472934290D8A95106188ACCDBE68C2C550D9890E4DD56F9", "nkeimhogjdpnpccoofpliimaahmaaome": "BE8EC205FB13EA9FFE953C42DC78A0884F7B6E39A8E7B26923B16A81AE388526"}, "ui": {"developer_mode": "2DE3F01C9A7BDC6722497902DF5A33B752C792732DB94E40AEDF6545A9915A38"}}, "google": {"services": {"account_id": "AF08DE102CF7D4F4350791372D43E3B9A969745411A8D01B0E4943BB35F05E57", "last_signed_in_username": "D9E96C861B7BC17D3B806CD1B2911B7E498DC77045FF53A5BC9FF9CCAC415C8A", "last_username": "8BE16F3A0945F3F42F2816A3FE79F0321A15782C9EFB0934F1AD8746B4AC98BF"}}, "homepage": "BA0C8CB4E3A7232D9E2F79149086BED063942FEBEB2BB44C907981F2A4BC1645", "homepage_is_newtabpage": "AFDCF6C23DC59384E1EA6A57C37CCE661C215094774B848B9F2C0A584B8BF935", "media": {"cdm": {"origin_data": "9727D004FF415E744251DE23C26612C6D9CE9B0E2EEE7CB69BDBF806AF6A019C"}, "storage_id_salt": "071A79329DC6F233DFB7B54304D9915957D86A7CA88A014DC52D7B45678AE824"}, "module_blocklist_cache_md5_digest": "89E31B414D4DFF965CD74CA3D61DE7916F8024E1C7BA2C0EA33CB4F46E1ECDAE", "pinned_tabs": "7914F053513EE8091AD96E97EAA46F225BE7814B226AFAB2FF574F051813AD72", "prefs": {"preference_reset_time": "275DFD9438FEC2165D6C0F9C2311917250F442E4216E33916AAE56E0E1D59744"}, "safebrowsing": {"incidents_sent": "15B921689ACA05548FCADF8CAF5D4F836639B357C2A97275F23D0B4C84E77BCD"}, "search_provider_overrides": "79A09F3DA9D199ED9B86E00F100B22DEAA7DF6856EAA6C87DFC5E1B82A6F67AC", "session": {"restore_on_startup": "EF1C394CBE508C3EA4584E77538305BB4E56992F5C530F911AA78BEFEBF81286", "startup_urls": "822C51F3C64D6D8863567548C6958E6E0F6E5872AC953DF4915359EA9AB066FF"}}, "super_mac": "5EB15EFA5DFBE726B2B1734A8B689547ED1F9230A31336C12801E3D718280882"}}