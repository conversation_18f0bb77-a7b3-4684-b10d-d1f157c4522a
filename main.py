import time
from selenium.webdriver.common.by import By
from chrome_debugger import ChromeDebugger
from base_page import BasePage


def main():
    # 初始化变量
    debugger = None
    try:
        # 配置路径
        data_dir = "./chrome_data"  # 自定义数据目录
        driver_path = "./chromedriver-win32/chromedriver.exe"  # 替换为你的chromedriver路径

        # 使用ChromeDebugger启动浏览器
        debugger = ChromeDebugger(
            data_dir=data_dir,
            driver_path=driver_path
        )

        # 元素定位
        locsy = (By.XPATH, '//*[@id ="J_logo_extend"]')  # 京东首页图标
        locdd = (By.XPATH, '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')  # 我的订单
        locsynb = (By.XPATH, '//*[@id="ttbar-home"]/a')  # 京东首页
        locss = (By.XPATH, '//*[@id="key"]')  # 搜索框

        # 启动浏览器并连接
        debugger.start_browser("https://www.jd.com")
        driver = debugger.connect_debugger()

        # 初始化基础页面操作类
        base_page = BasePage(driver)

        # 使用拟人化鼠标操作点击我的订单
        base_page.base_mouse_action("click", locdd)
        time.sleep(3)

        # 切换到新窗口
        base_page.base_switch_window(index=1)
        time.sleep(3)

        # 使用拟人化鼠标操作点击京东首页
        base_page.base_mouse_action("click", locsynb)
        time.sleep(3)

        # 切换到最新窗口
        base_page.base_switch_window(index=2)
        time.sleep(3)

        # 使用拟人化鼠标操作点击搜索框
        base_page.base_mouse_action("click", locss)
        # 使用键盘操作输入文本
        base_page.base_keyboard_action("send_keys", locss, "家居小店")
        time.sleep(100)

    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
    finally:
        # 关闭浏览器
        if debugger is not None:
            debugger.close_browser()
            print("浏览器已关闭")


if __name__ == "__main__":
    main()