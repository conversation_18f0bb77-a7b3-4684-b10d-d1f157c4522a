import logging
import os
import time


def setup_logger(name, log_dir="logs"):
    """
    配置日志系统

    :param name: 日志器名称
    :param log_dir: 日志存储目录
    :return: 配置好的日志器
    """
    # 创建日志目录
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 日志文件名（包含日期）
    current_date = time.strftime('%Y%m%d')
    log_file = os.path.join(log_dir, f"{name}_{current_date}.log")

    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    # 避免重复添加处理器
    if logger.handlers:
        return logger

    # 格式器 - 包含时间、日志器名称、级别和消息
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 文件处理器 - 写入文件，指定UTF-8编码解决乱码
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 控制台处理器 - 输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger
