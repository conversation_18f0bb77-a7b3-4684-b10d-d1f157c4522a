2025-08-01 10:34:34 - BasePage - INFO - 尝试打开URL: www.baidu.com
2025-08-01 10:34:34 - BasePage - ERROR - 打开URL失败: www.baidu.com，错误: Message: invalid argument
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7713ee]
	(No symbol) [0x0x76faa9]
	(No symbol) [0x0x7701fb]
	(No symbol) [0x0x784a6e]
	(No symbol) [0x0x8105f7]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:34:35 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801103434.png
2025-08-01 10:34:35 - BasePage - INFO - 元素不存在: ('id', 'kw'), 原因: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="kw"]"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x7c97ae]
	(No symbol) [0x0x7c9b4b]
	(No symbol) [0x0x8121f2]
	(No symbol) [0x0x7ee4f4]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:35:13 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('id', 'su')
2025-08-01 10:35:13 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:35:37 - BasePage - INFO - 尝试打开URL: www.baidu.com
2025-08-01 10:35:37 - BasePage - ERROR - 打开URL失败: www.baidu.com，错误: Message: invalid argument
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7713ee]
	(No symbol) [0x0x76faa9]
	(No symbol) [0x0x7701fb]
	(No symbol) [0x0x784a6e]
	(No symbol) [0x0x8105f7]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:35:37 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801103537.png
2025-08-01 10:35:37 - BasePage - INFO - 元素不存在: ('id', 'kw'), 原因: Message: no such element: Unable to locate element: {"method":"css selector","selector":"[id="kw"]"}
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#nosuchelementexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x7c97ae]
	(No symbol) [0x0x7c9b4b]
	(No symbol) [0x0x8121f2]
	(No symbol) [0x0x7ee4f4]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:36:55 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('id', 'su')
2025-08-01 10:36:55 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801103655.png
2025-08-01 10:36:59 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="content_left"]//h3/a')
2025-08-01 10:36:59 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:36:59 - BasePage - INFO - 运单号转换: Y123456789 -> P123456789
2025-08-01 10:37:10 - BasePage - INFO - 元素存在: ('id', 'kw')
2025-08-01 10:37:10 - BasePage - INFO - 向元素输入内容: Selenium
2025-08-01 10:37:10 - BasePage - INFO - 元素被点击: ('id', 'su')
2025-08-01 10:37:13 - BasePage - INFO - 获取元素文本: selenium - 百度翻译
2025-08-01 10:37:13 - BasePage - INFO - 运单号转换: Y123456789 -> P123456789
2025-08-01 10:38:23 - BasePage - INFO - 尝试打开URL: https://www.baidu.com
2025-08-01 10:38:54 - BasePage - INFO - 成功打开URL: https://www.baidu.com，页面标题: 百度一下，你就知道
2025-08-01 10:38:54 - BasePage - INFO - 元素存在: ('id', 'kw')
2025-08-01 10:38:54 - BasePage - INFO - 向元素输入内容: Selenium
2025-08-01 10:38:55 - BasePage - ERROR - 无法单击元素, 定位: ('id', 'su'), 错误: Message: element click intercepted: Element is not clickable at point (669, 35)
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#elementclickinterceptedexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x7cfad0]
	(No symbol) [0x0x7cde8a]
	(No symbol) [0x0x7cba07]
	(No symbol) [0x0x7cad0b]
	(No symbol) [0x0x7bf365]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x7bedf4]
	(No symbol) [0x0x7ee724]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 10:38:55 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801103855.png
2025-08-01 10:39:28 - BasePage - ERROR - 【显性等待】30秒超时, 等待方式: visible, 元素定位: ('xpath', '//*[@id="content_left"]//h3/a')
2025-08-01 10:39:29 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801103928.png
2025-08-01 10:39:29 - BasePage - INFO - 运单号转换: Y123456789 -> P123456789
2025-08-01 10:40:48 - BasePage - INFO - 尝试打开URL: https://www.baidu.com
2025-08-01 10:40:49 - BasePage - INFO - 成功打开URL: https://www.baidu.com，页面标题: 百度一下，你就知道
2025-08-01 10:40:49 - BasePage - INFO - 尝试打开URL: https://www.uc123.com
2025-08-01 10:41:11 - BasePage - INFO - 成功打开URL: https://www.uc123.com，页面标题: UC导航_极速上网体验
2025-08-01 10:41:11 - BasePage - INFO - 尝试打开URL: https://www.hao123.com
2025-08-01 10:41:12 - BasePage - INFO - 成功打开URL: https://www.hao123.com，页面标题: hao123_上网从这里开始
2025-08-01 10:41:12 - BasePage - INFO - 尝试打开URL: https://www.bilibili.com
2025-08-01 10:41:14 - BasePage - INFO - 成功打开URL: https://www.bilibili.com，页面标题: 哔哩哔哩 (゜-゜)つロ 干杯~-bilibili
2025-08-01 10:41:35 - BasePage - INFO - 尝试打开URL: https://www.baidu.com
2025-08-01 10:41:36 - BasePage - INFO - 成功打开URL: https://www.baidu.com，页面标题: 百度一下，你就知道
2025-08-01 10:41:36 - BasePage - INFO - 尝试打开URL: https://www.uc123.com
2025-08-01 10:42:29 - BasePage - INFO - 成功打开URL: https://www.uc123.com，页面标题: UC导航_极速上网体验
2025-08-01 10:42:29 - BasePage - INFO - 尝试打开URL: https://www.hao123.com
2025-08-01 10:43:27 - BasePage - INFO - 成功打开URL: https://www.hao123.com，页面标题: hao123_上网从这里开始
2025-08-01 10:43:27 - BasePage - INFO - 尝试打开URL: https://www.bilibili.com
2025-08-01 10:43:28 - BasePage - INFO - 成功打开URL: https://www.bilibili.com，页面标题: 哔哩哔哩 (゜-゜)つロ 干杯~-bilibili
2025-08-01 10:44:19 - BasePage - INFO - 尝试打开URL: https://www.baidu.com
2025-08-01 10:44:19 - BasePage - INFO - 成功打开URL: https://www.baidu.com
2025-08-01 10:44:19 - BasePage - INFO - 尝试打开URL: https://www.uc123.com
2025-08-01 10:44:36 - BasePage - INFO - 成功打开URL: https://www.uc123.com
2025-08-01 10:44:36 - BasePage - INFO - 尝试打开URL: https://www.hao123.com
2025-08-01 10:44:38 - BasePage - INFO - 成功打开URL: https://www.hao123.com
2025-08-01 10:44:38 - BasePage - INFO - 尝试打开URL: https://www.bilibili.com
2025-08-01 10:44:39 - BasePage - INFO - 成功打开URL: https://www.bilibili.com
2025-08-01 10:46:12 - BasePage - INFO - 尝试打开URL: https://www.baidu.com
2025-08-01 10:46:12 - BasePage - INFO - 成功打开URL: https://www.baidu.com
2025-08-01 10:46:12 - BasePage - INFO - 尝试打开URL: https://www.uc123.com
2025-08-01 10:46:18 - BasePage - INFO - 成功打开URL: https://www.uc123.com
2025-08-01 10:46:18 - BasePage - INFO - 尝试打开URL: https://www.hao123.com
2025-08-01 10:46:19 - BasePage - INFO - 成功打开URL: https://www.hao123.com
2025-08-01 10:46:19 - BasePage - INFO - 尝试打开URL: https://www.bilibili.com
2025-08-01 10:46:20 - BasePage - INFO - 成功打开URL: https://www.bilibili.com
2025-08-01 10:55:19 - BasePage - ERROR - 查找元素发生错误: WebDriver.find_element() takes from 1 to 3 positional arguments but 22 were given, 元素定位: (By.ID, 'J_searchbg')
2025-08-01 10:55:19 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801105519.png
2025-08-01 10:56:36 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 10:56:36 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801105636.png
2025-08-01 10:58:57 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 10:59:35 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:00:41 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:00:41 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:00:49 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:01:16 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:01:16 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:01:42 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:02:04 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:02:04 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:07:11 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:07:11 - BasePage - INFO - 元素被点击: 商品
2025-08-01 11:07:11 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:08:14 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:08:14 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:08:28 - BasePage - INFO - 页面已刷新
2025-08-01 11:08:29 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:08:57 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:08:57 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:09:15 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:09:15 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:09:38 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:09:38 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:10:03 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:10:06 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:11:23 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:11:23 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801111123.png
2025-08-01 11:12:16 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:12:19 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:13:36 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:13:37 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801111336.png
2025-08-01 11:14:28 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:14:28 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:15:55 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:15:55 - BasePage - ERROR - 输入内容失败, 定位: ('xpath', '//*[@id="J_searchbg"]'), 错误: Message: invalid element state
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7c0f77]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x7bedf4]
	(No symbol) [0x0x7ee724]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:15:56 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801111555.png
2025-08-01 11:16:28 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:16:28 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:16:58 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:16:58 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:19:00 - BasePage - INFO - 元素存在: ('id', 'kw')
2025-08-01 11:19:00 - BasePage - INFO - 向元素输入内容: Selenium
2025-08-01 11:21:52 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:21:57 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:23:14 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:23:14 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801112314.png
2025-08-01 11:27:59 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:28:04 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:28:04 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:29:13 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:29:18 - BasePage - INFO - 向元素输入内容: snack家居小店
2025-08-01 11:29:18 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:30:36 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:30:36 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801113036.png
2025-08-01 11:33:57 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:34:02 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 11:34:02 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:35:19 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:35:20 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801113519.png
2025-08-01 11:36:39 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:36:44 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 11:36:44 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:37:53 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:37:58 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id="key"]')
2025-08-01 11:37:59 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 11:37:59 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:39:17 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:39:17 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801113917.png
2025-08-01 11:50:18 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:50:19 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id="key"]')
2025-08-01 11:50:21 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 11:50:21 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:51:07 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:51:07 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 11:53:48 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 11:53:49 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id="key"]')
2025-08-01 11:53:50 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: B8112B7AEA
2025-08-01 11:53:50 - BasePage - INFO - 切换到新窗口，句柄: 479F295A84
2025-08-01 11:53:50 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 11:53:51 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 11:53:51 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 11:55:08 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 11:55:09 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801115508.png
2025-08-01 13:20:06 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:20:06 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: DEB5CAF334
2025-08-01 13:20:06 - BasePage - INFO - 切换到新窗口，句柄: C54EC33309
2025-08-01 13:20:07 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:20:07 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id="key"]')
2025-08-01 13:20:07 - BasePage - INFO - 使用输入方式 1 尝试输入: snack家居小店
2025-08-01 13:20:07 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 13:20:26 - BasePage - ERROR - 查找元素发生错误: 'NoneType' object has no attribute 'is_displayed', 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 13:20:26 - BasePage - ERROR - 截屏失败: Message: no such window: target window already closed
from unknown error: web view not found
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x75ff09]
	(No symbol) [0x0x7f4f8e]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 13:31:05 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:31:05 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 7649FE7675
2025-08-01 13:31:06 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:31:06 - BasePage - INFO - 元素被点击: ('xpath', '//*[@id="key"]')
2025-08-01 13:31:06 - BasePage - INFO - 输入验证成功，元素值为: snack家居小店
2025-08-01 13:32:23 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 13:32:24 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801133223.png
2025-08-01 13:42:32 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:42:32 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 4ECDB959A3
2025-08-01 13:42:34 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:42:34 - BasePage - ERROR - send_keys动作的keys必须是字符串
2025-08-01 13:43:22 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:43:23 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: D3E0088479
2025-08-01 13:43:24 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:43:24 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: snack家居小店
2025-08-01 13:44:41 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 13:44:41 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801134441.png
2025-08-01 13:45:05 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:45:15 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 900A797388
2025-08-01 13:45:15 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:45:25 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: snack家居小店
2025-08-01 13:46:52 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="searchStoreList"]/div[1]/div/div/div[1]/div/div[3]/span[2]')
2025-08-01 13:46:53 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801134652.png
2025-08-01 13:47:20 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:47:21 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: C1E235759C
2025-08-01 13:47:21 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 13:47:23 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: snack家居小店
2025-08-01 13:49:26 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id ="J_logo_extend"]')
2025-08-01 13:49:26 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 13:49:27 - BasePage - ERROR - 切换窗口失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 13:49:27 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 13:49:28 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="key"]')
2025-08-01 13:49:28 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 14:24:35 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:24:35 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 7EF272CBA8
2025-08-01 14:24:36 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:24:36 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:24:42 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:25:38 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:25:38 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: DFC97ACE8C
2025-08-01 14:25:39 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:25:40 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:25:40 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: FEA82F1C80
2025-08-01 14:25:40 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:25:41 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:26:12 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:26:12 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: F533B74103
2025-08-01 14:26:13 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:26:14 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:26:14 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: 4ABE8AC135
2025-08-01 14:26:15 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:26:15 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:29:00 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:29:03 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 020CEE05BF
2025-08-01 14:29:03 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:29:06 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:29:09 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: CA2AA95198
2025-08-01 14:29:09 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:29:13 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:30:47 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:30:50 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 740998A5DE
2025-08-01 14:30:50 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:30:54 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:30:57 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: E182DBE7B7
2025-08-01 14:30:57 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:31:00 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:31:39 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:31:42 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 0DF2CB555E
2025-08-01 14:31:42 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:31:46 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:31:49 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: EC285B9DC1
2025-08-01 14:31:50 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:31:54 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="key"]')
2025-08-01 14:31:54 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 14:38:47 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 14:38:50 - BasePage - INFO - 当前窗口数量: 2, 当前窗口句柄: 03794D4C60
2025-08-01 14:38:50 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 14:38:53 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 14:38:56 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: A532A0A9BE
2025-08-01 14:38:56 - BasePage - INFO - 切换后页面标题: 京东(JD.COM)-正品低价、品质保障、配送及时、轻松购物！, URL: https://www.jd.com/
2025-08-01 14:38:59 - BasePage - INFO - 执行鼠标动作: click，元素: ('xpath', '//*[@id="key"]')
2025-08-01 14:38:59 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 15:11:22 - BasePage - ERROR - 鼠标操作失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x8180ff]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 15:11:22 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801151122.png
2025-08-01 15:11:25 - BasePage - INFO - 当前窗口数量: 1, 当前窗口句柄: 3510583B16
2025-08-01 15:11:25 - BasePage - ERROR - 未指定有效的切换目标
2025-08-01 15:12:45 - BasePage - ERROR - 【显性等待】77秒超时, 等待方式: clickable, 元素定位: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 15:12:45 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801151245.png
2025-08-01 15:12:48 - BasePage - INFO - 当前窗口数量: 3, 当前窗口句柄: 3510583B16
2025-08-01 15:12:48 - BasePage - INFO - 切换后页面标题: 我的京东--我的订单, URL: https://order.jd.com/center/list.action
2025-08-01 15:12:57 - BasePage - ERROR - 鼠标操作失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x8180ff]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="key"]')
2025-08-01 15:12:57 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801151257.png
2025-08-01 15:13:00 - BasePage - INFO - 向元素('xpath', '//*[@id="key"]')输入文本: 家居小店
2025-08-01 15:14:28 - BasePage - ERROR - 鼠标操作失败: Message: move target out of bounds
  (Session info: chrome=138.0.7204.184)
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x8180ff]
	(No symbol) [0x0x7ee4ac]
	(No symbol) [0x0x80f9e3]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="shortcut"]/div/ul[2]/li[3]/div/a')
2025-08-01 15:14:28 - BasePage - INFO - 屏幕截图已保存: D:\PythonProject\Rpa_Automation\screenshots\20250801151428.png
2025-08-01 15:14:31 - BasePage - INFO - 当前窗口数量: 1, 当前窗口句柄: 519861D9FC
2025-08-01 15:14:31 - BasePage - ERROR - 未指定有效的切换目标
2025-08-01 15:15:16 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id: session deleted as the browser has closed the connection
from disconnected: not connected to DevTools
  (Session info: chrome=138.0.7204.184); For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x781053]
	(No symbol) [0x0x770830]
	(No symbol) [0x0x78e7ef]
	(No symbol) [0x0x7f515c]
	(No symbol) [0x0x80f2f9]
	(No symbol) [0x0x7ee2a6]
	(No symbol) [0x0x7bd5f0]
	(No symbol) [0x0x7be464]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	GetHandleVerifier [0x0x977158+95672]
	GetHandleVerifier [0x0x977300+96096]
	GetHandleVerifier [0x0x9621aa+9738]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="ttbar-home"]/a')
2025-08-01 15:15:16 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 15:15:19 - BasePage - ERROR - 切换窗口失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 15:15:19 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 15:15:22 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="key"]')
2025-08-01 15:15:22 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

2025-08-01 15:15:22 - BasePage - ERROR - 查找元素发生错误: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]
, 元素定位: ('xpath', '//*[@id="key"]')
2025-08-01 15:15:22 - BasePage - ERROR - 截屏失败: Message: invalid session id; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalidsessionidexception
Stacktrace:
	GetHandleVerifier [0x0x96f173+62931]
	GetHandleVerifier [0x0x96f1b4+62996]
	(No symbol) [0x0x780ece]
	(No symbol) [0x0x7bc8b8]
	(No symbol) [0x0x7ee366]
	(No symbol) [0x0x7e9e12]
	(No symbol) [0x0x7e93a6]
	(No symbol) [0x0x753a05]
	(No symbol) [0x0x753f5e]
	(No symbol) [0x0x7543ed]
	GetHandleVerifier [0x0xbc3463+2504899]
	GetHandleVerifier [0x0xbbe892+2485490]
	GetHandleVerifier [0x0x99590a+220522]
	GetHandleVerifier [0x0x986388+157672]
	GetHandleVerifier [0x0x98cb6d+184269]
	(No symbol) [0x0x7536d0]
	(No symbol) [0x0x752edd]
	GetHandleVerifier [0x0xce96ec+3709772]
	BaseThreadInitThunk [0x0x75a1fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

